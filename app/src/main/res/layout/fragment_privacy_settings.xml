<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.settings.privacy.PrivacyViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:indeterminate="true"
            app:goneIfNot="@{viewModel.isPrivacyDataLoading}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fitsSystemWindows="true"
            app:goneIf="@{viewModel.isPrivacyDataLoading}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/custom_action_bar"
                layout="@layout/item_custom_action_bar_rating" />

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:applySystemBarInsets="@{`bottom`}"
                    android:orientation="vertical">

                    <include
                        android:id="@+id/private_setting_last_seen"
                        layout="@layout/item_settings"
                         />

                    <include
                        android:id="@+id/private_setting_online_status"
                        layout="@layout/item_settings"
                        />

                    <include
                        android:id="@+id/private_setting_profile_photo"
                        layout="@layout/item_settings"
                         />

                    <include
                        android:id="@+id/private_setting_last_about"
                        layout="@layout/item_settings"
                         />

                    <include
                        android:id="@+id/country_flag"
                        goneIfNot="@{viewModel.isPricacyFlagVisible}"
                        layout="@layout/item_settings_switch"
                        app:isChecked="@{viewModel.hasToDisplayCountry}"/>

                    <include
                        android:id="@+id/private_setting_blocked_stars"
                        layout="@layout/item_settings"
                        app:helpText="@{viewModel.blockedStarCount != null ? @plurals/setting_privacy_users(viewModel.blockedStarCount, viewModel.blockedStarCount) : ``}"
                         />

                    <include
                        android:id="@+id/private_setting_broadcast_privacy"
                        layout="@layout/item_settings"
                        app:helpText="@{viewModel.blockedStarCount != null ? @plurals/setting_privacy_users(viewModel.blockedBroadcastCount,viewModel.blockedBroadcastCount) : ``}"
                        />


                    <include
                        android:id="@+id/private_setting_message_privacy"
                        goneIfNot="@{viewModel.userAccount.Profile.premium}"
                        layout="@layout/item_settings"/>

                    <include
                        android:id="@+id/private_setting_blocked_messages"
                        layout="@layout/item_settings"
                        app:helpText="@{viewModel.blockedMessageCount>0?viewModel.blockedMessageCount.toString():``}" />
                    />

                    <include
                        android:id="@+id/private_setting_blocked_huddles"
                        layout="@layout/item_settings"
                        app:helpText="@{viewModel.blockedHuddleCount>0?viewModel.blockedHuddleCount.toString():``}" />

                    <include
                        android:id="@+id/private_setting_Podium_privacy"
                        layout="@layout/item_settings"
                        goneIfNot="@{viewModel.privacyPodiumVisible}"/>

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

