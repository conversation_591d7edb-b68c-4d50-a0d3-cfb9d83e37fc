<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="viewModel"
            type="com.app.messej.ui.premium.SubscribeGoldenBottomSheetViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/colorSurface"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/activity_margin"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:padding="@dimen/activity_margin"
                   android:background="@drawable/ic_golden_membership_bg"
                    app:cardCornerRadius="5dp"
                    app:cardUseCompatPadding="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/ic_golden_membership_bg">
                    </androidx.appcompat.widget.AppCompatImageView>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="@dimen/activity_margin">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/img_membership_type"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_gold_membership_profile" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_membership"
                            style="@style/TextAppearance.Flashat.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:text="@string/title_premium_membership"
                            android:textColor="@color/textColorSecondaryLight"
                            app:layout_constraintStart_toEndOf="@id/img_membership_type"
                            app:layout_constraintTop_toTopOf="@+id/img_membership_type" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_premium_subscription"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/golden_subscription"
                            android:textColor="@color/chatFormatColorAlwaysLightPurple"
                            app:layout_constraintStart_toStartOf="@id/txt_membership"
                            app:layout_constraintTop_toBottomOf="@+id/txt_membership" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/txt_premium_subscription_status"
                            android:textAppearance="@style/TextAppearance.Flashat.Label.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            tools:text="Active"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/txt_membership"
                            app:layout_constraintTop_toTopOf="@+id/txt_membership" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:padding="@dimen/activity_margin"
                    app:cardBackgroundColor="@color/colorSurfaceSecondary"
                    app:cardCornerRadius="5dp"
                    app:cardUseCompatPadding="true"
                    app:contentPadding="4dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="@dimen/activity_margin">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_billed_amount_title"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.expired?@string/title_premium_renewal_amount:@string/title_premium_billed_amount}"
                            app:layout_constraintBottom_toBottomOf="@+id/txt_bill_period"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/txt_billed_amount"
                            tools:text="@string/title_premium_billed_amount" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_billed_amount"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="1500 FLiX"
                            android:text="@{@string/podium_buy_camera_required_flix(viewModel.price)}"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_bill_period"
                            style="@style/TextAppearance.Flashat.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/subscription_billing_period"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/txt_billed_amount" />

                        <View
                            android:id="@+id/billed_amount_under_line"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:background="@color/textColorBusinessPrimary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/txt_bill_period" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_bill_date_title"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.isExpired()?@string/title_premium_expired_on:@string/title_premium_next_bill_date}"
                            app:layout_constraintBottom_toBottomOf="@+id/txt_expiry"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/txt_next_bill_date"
                            tools:text="@string/title_premium_next_bill_date"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_next_bill_date"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.nextBillDate}"
                            android:layout_marginTop="10dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/billed_amount_under_line" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/txt_expiry"
                            style="@style/TextAppearance.Flashat.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/txt_next_bill_date" />

                        <View
                            android:id="@+id/txt_expiry_under_line"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:background="@color/textColorBusinessPrimary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/txt_expiry" />

                        <Button
                            android:id="@+id/action_restore_minister"
                            style="@style/Widget.Flashat.LargeRoundedButton"
                            android:layout_width="match_parent"
                            goneIfNot="@{viewModel.isRestoreMinisterEligible()}"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/double_margin"
                            tools:text="@string/golden_restore_to_minister"
                            android:text="@string/golden_restore_to_minister"
                            app:layout_constraintTop_toBottomOf="@+id/txt_expiry_under_line" />

                        <Button
                            android:id="@+id/action_renew"
                            style="@style/Widget.Flashat.LargeRoundedButton"
                            android:layout_width="match_parent"
                            goneIfNot="@{viewModel.isExpired()}"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/double_margin"
                            tools:text="@string/title_renew_subscription"
                            android:text="@string/title_renew_subscription"
                            app:layout_constraintTop_toBottomOf="@+id/action_restore_minister" />


                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_renewal_flix"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:padding="@dimen/activity_margin"
                    goneIf="@{viewModel.isExpired()}"
                    app:cardBackgroundColor="@color/colorSurfaceSecondary"
                    app:cardCornerRadius="5dp"
                    app:cardUseCompatPadding="true"
                    app:contentPadding="4dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/activity_margin">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/titles"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/header_auto_renewal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/action_switch"
                            style="@style/Widget.Flashat.Switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            app:layout_constraintBottom_toBottomOf="@id/titles"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/titles"
                            android:checked="@{viewModel.upgradeUserLevel.isAutoRenewal()}"
                            tools:checked="false" />

                        <View
                            android:id="@+id/divider"
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:background="@color/colorDivider"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/titles" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_note"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            style="@style/TextAppearance.Flashat.Body2"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/divider"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:text="@string/golden_renewal_note"/>


                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</layout>