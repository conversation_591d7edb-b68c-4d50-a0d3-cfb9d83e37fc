<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.app.messej.data.model.enums.UserRole"/>
        <variable name="message" type="com.app.messej.data.model.entity.HuddleChatMessage"/>
        <variable name="senderName" type="String"/>
        <variable name="showUpgrade" type="Boolean"/>
        <variable name="selected" type="Boolean"/>
        <variable name="isOwnMessage" type="Boolean"/>
        <variable name="relation" type="String"/>
        <variable
            name="isGiftBlocked"
            type="Boolean" />
        <variable name="showFollow" type="Boolean"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/chat_holder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:foreground="@{selected?@color/colorChatSelectedBG:@color/transparent}"
        tools:foreground="@color/transparent">

        <View
            android:id="@+id/highlightView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:elevation="1dp"
            android:background="@color/colorChatSelectedBG"
            android:visibility="gone"
            tools:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/chat_bubble"
            style="@style/Widget.Flashat.ChatBubble.Huddle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:layout_marginVertical="@dimen/element_spacing"
            android:background="@drawable/bg_chat_bubble_huddle_outgoing"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/header_notch"
                layout="@layout/layout_huddle_header_tag"
                app:goneIfNot="@{message.needsHeaderNotch}" />

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/user_dp"
                android:layout_width="@dimen/chat_huddle_dp_size"
                android:layout_height="@dimen/chat_huddle_dp_size"
                android:layout_marginStart="@dimen/activity_margin"
                android:layout_marginTop="@dimen/activity_margin"
                android:elevation="2dp"
                android:scaleType="centerCrop"
                app:imageUrl="@{message.senderDetails.thumbnail}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                app:riv_corner_radius="@dimen/superstar_list_dp_size"
                tools:src="@drawable/im_user_placeholder_opaque" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/superstar_premium_badge"
                android:layout_width="@dimen/premium_badge_size"
                android:layout_height="@dimen/premium_badge_size"
                android:elevation="4dp"
                app:layout_constraintStart_toStartOf="@id/user_dp"
                app:layout_constraintTop_toTopOf="@id/user_dp"
                app:userBadge="@{message.senderDetails.userBadge}"
                tools:src="@drawable/ic_user_badge_premium" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/content_barrier"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:barrierAllowsGoneWidgets="true"
                app:barrierDirection="end"
                app:constraint_referenced_ids="user_dp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/chat_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constrainedWidth="true"
                android:layout_marginStart="@dimen/activity_margin"
                android:layout_marginEnd="@dimen/line_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{senderName}"
                android:textAppearance="@style/TextAppearance.Flashat.ChatBubble.Huddle.Name"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintEnd_toStartOf="@+id/chat_flag"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/content_barrier"
                app:layout_constraintTop_toTopOf="@+id/user_dp"
                tools:text="John Doe with a long name"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/chat_flag"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                android:layout_marginStart="@dimen/line_spacing"
                android:layout_marginEnd="@dimen/line_spacing"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="@+id/chat_name"
                app:layout_constraintEnd_toStartOf="@+id/action_barrier"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/chat_name"
                app:layout_constraintTop_toTopOf="@+id/chat_name"
                tools:srcCompat="@drawable/flag_france" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/huddle_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constrainedWidth="true"
                android:drawableStart="@drawable/ic_huddle_my_posts_arrow"
                android:drawablePadding="@dimen/line_spacing"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintWidth_min="60dp"
                app:goneIfNullOrBlank="@{message.huddleName}"
                android:text="@{message.huddleName}"
                android:layout_marginStart="@dimen/element_spacing"
                app:layout_goneMarginStart="@dimen/line_spacing"
                android:textAppearance="@style/TextAppearance.Flashat.ChatBubble.HuddleName"
                app:layout_constraintStart_toEndOf="@+id/chat_flag"
                app:layout_constraintBottom_toBottomOf="@+id/chat_name"
                app:layout_constraintTop_toTopOf="@+id/chat_name"
                app:layout_constraintEnd_toStartOf="@+id/action_barrier"
                tools:text="Da Huddle That is Too Long"
                tools:visibility="visible"
                android:visibility="gone" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/chat_actions"
                style="@style/Widget.Flashat.Button.TextButton.IconOnly.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:icon="@drawable/ic_vertical_menu"
                app:iconTint="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/user_dp"
                tools:visibility="gone" />

            <View
                android:id="@+id/chat_actions_spacer"
                android:layout_width="1dp"
                android:layout_height="2dp"
                app:layout_goneMarginEnd="@dimen/activity_margin"
                app:layout_constraintEnd_toStartOf="@+id/chat_actions"
                app:layout_constraintTop_toTopOf="@+id/chat_actions"
                app:layout_constraintBottom_toBottomOf="@+id/chat_actions" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/chat_reported"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:paddingStart="@dimen/line_spacing"
                app:goneIfNot="@{message.reported}"
                app:layout_constraintBottom_toBottomOf="@+id/chat_actions_spacer"
                app:layout_constraintEnd_toStartOf="@+id/chat_actions_spacer"
                app:layout_constraintTop_toTopOf="@+id/chat_actions_spacer"
                app:srcCompat="@drawable/ic_chat_report"
                app:tint="@color/colorChatTextSecondary"
                tools:visibility="visible" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/upgrade_button"
                style="@style/Widget.Flashat.TinyRoundedButton.CustomBG"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/line_spacing"
                android:background="@drawable/bg_huddle_chat_upgrade_button"
                android:minWidth="50dp"
                android:text="@string/common_upgrade"
                android:visibility="gone"
                app:goneIfNot="@{showUpgrade}"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintEnd_toStartOf="@+id/chat_reported"
                app:layout_constraintTop_toTopOf="@+id/user_dp"
                tools:visibility="visible" />

            <Button
                android:id="@+id/user_follow"
                style="@style/Widget.Flashat.MiniRoundedButton.Outline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="25dp"
                android:paddingHorizontal="6dp"
                android:layout_marginEnd="@dimen/element_spacing"
                android:text="@string/user_action_follow"
                android:visibility="visible"
                app:goneIfNot="@{showFollow}"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintEnd_toStartOf="@+id/upgrade_button"
                app:layout_constraintTop_toTopOf="@+id/user_dp"
                tools:visibility="gone" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/chat_relation_ribbon"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                app:goneIf="@{relation==null}"
                tools:visibility="visible"
                android:layout_marginEnd="@dimen/element_spacing"
                app:layout_goneMarginEnd="0dp"
                app:layout_constraintEnd_toStartOf="@+id/user_follow"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintTop_toTopOf="@+id/user_dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView12"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/bg_chat_relation_ribbon_tail" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_chat_relation_ribbon"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/appCompatImageView12"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/appCompatTextView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="@dimen/element_spacing"
                    android:text="@{relation}"
                    tools:text="LIKER"
                    android:textAppearance="@style/TextAppearance.Flashat.Caption.Bold"
                    android:textColor="@color/colorSecondaryLight"
                    android:textAllCaps="true"
                    android:textSize="10sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/appCompatImageView12"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/chat_edited"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/chat_huddle_edited"
                android:layout_marginEnd="@dimen/line_spacing"
                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                android:textColor="@color/colorChatTextSecondaryLight"
                android:textSize="11sp"
                android:textStyle="italic"
                android:visibility="gone"
                app:goneIfNot="@{message.edited}"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintEnd_toStartOf="@+id/chat_relation_ribbon"
                app:layout_constraintTop_toTopOf="@+id/user_dp" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/action_barrier"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:barrierAllowsGoneWidgets="true"
                app:barrierDirection="start"
                app:constraint_referenced_ids="chat_relation_ribbon,upgrade_button,chat_edited" />

            <com.webtoonscorp.android.readmore.ReadMoreTextView
                android:id="@+id/chat_message"
                style="@style/Widget.Flashat.ChatBubble.Message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/activity_margin"
                android:textAppearance="@style/TextAppearance.Flashat.ChatBubble.Huddle.Message"
                tools:textColor="@color/colorChatTextSecondaryLight"
                app:goneIfNot="@{message.hasText || message.reportedOrBlocked}"
                app:layout_constraintEnd_toEndOf="parent"
                app:chatTextColor="@{message.showChatTextColor?message.chatTextColor:null}"
                app:defaultChatTextColor="@{message.reportedOrBlocked?@color/colorChatTextSecondaryLight:@color/colorChatTextSecondary}"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="@+id/chat_name"
                app:layout_constraintTop_toBottomOf="@+id/user_dp"
                tools:text="Hello Everyone"
                tools:visibility="visible" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/media_holder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/activity_margin"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/content_barrier"
                app:layout_constraintTop_toBottomOf="@+id/chat_message"
                tools:visibility="gone">

                <!--                    <include layout="@layout/item_chat_message_media_image" />-->

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/reply_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_margin"
                android:text="@string/chat_huddle_reply_title"
                app:goneIfNot="@{message.hasReply &amp;&amp; message.reported == false}"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                android:textColor="@color/colorChatTextSecondaryLight"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="@+id/chat_message"
                app:layout_constraintTop_toBottomOf="@+id/media_holder" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/reply_holder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/activity_margin"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/content_barrier"
                app:layout_constraintTop_toBottomOf="@+id/reply_title"
                tools:visibility="visible">

                <!--                    <include layout="@layout/item_chat_message_huddle_reply_to" />-->

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/footer_barrier"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:barrierAllowsGoneWidgets="true"
                app:barrierDirection="bottom"
                app:barrierMargin="@dimen/element_spacing"
                app:constraint_referenced_ids="reply_holder" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/left_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/element_spacing"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/comments_layout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/footer_barrier">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/chat_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    goneIf="@{message.senderDetails.blockedByEitherAdmin == true}"
                    android:text="@{message.formattedCreatedDateTime}"
                    android:textAppearance="@style/TextAppearance.Flashat.ChatBubble.Time"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/forward_layout"
                    tools:text="22/03/2023 | 12:30" />

                <include layout="@layout/item_chat_message_forward_tag"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:show="@{message.forwardId!=null}"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/gift_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/activity_margin"
                android:background="@drawable/bg_gift_huddle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/footer_barrier"
                app:goneIf="@{isGiftBlocked || message.senderDetails.citizenship.visitor}"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/gift_huddle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_gift_square"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"

                    />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/TextAppearance.AppCompat.Caption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/line_spacing"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintBottom_toBottomOf="@+id/gift_huddle"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textSize="11sp"
                    android:text="@{message.giftCountFormatted}"
                    android:layout_marginEnd="@dimen/line_spacing"
                    app:layout_constraintStart_toEndOf="@+id/gift_huddle"
                    app:layout_constraintTop_toTopOf="@+id/gift_huddle"
                    tools:text="999">

                </androidx.appcompat.widget.AppCompatTextView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/comments_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_margin"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:paddingTop="@dimen/element_spacing"
                android:paddingBottom="@dimen/line_spacing"
                app:goneIf="@{message.reported == true || message.senderDetails.premium == false}"

                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/footer_barrier">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/comments_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_huddle_post_comments"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/comments_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/common_comments"
                        android:textAllCaps="true"
                        android:textAppearance="@style/TextAppearance.Flashat.Caption.Bold"
                        android:textColor="@color/colorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/icon_comments"
                        android:layout_width="wrap_content"
                        android:layout_height="8dp"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_comments"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/comments_label"
                        app:layout_constraintTop_toTopOf="parent">

                    </androidx.appcompat.widget.AppCompatImageView>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/comments_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:text="@{message.commentsCountFormatted}"
                        android:textAppearance="@style/TextAppearance.Flashat.Caption.Bold"
                        android:textColor="@color/colorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/icon_comments"
                        tools:text="69" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/bg_huddle_post_comments_downward_arrow"
                    app:layout_constraintEnd_toEndOf="@id/comments_tag"
                    app:layout_constraintStart_toStartOf="@id/comments_tag"
                    app:layout_constraintTop_toBottomOf="@id/comments_tag" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- <com.google.android.material.button.MaterialButton
                 android:id="@+id/reply_icon"
                 style="@style/Widget.Flashat.Button.TextButton.IconOnly.Small.Primary"
                 app:goneIfNot="@{message.reported == false}"
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content"
                 app:icon="@drawable/ic_reply"
                 app:layout_constraintBottom_toBottomOf="@+id/comments_layout"
                 app:layout_constraintEnd_toStartOf="@+id/like_button"
                 app:layout_constraintTop_toTopOf="@+id/comments_layout" />

             <com.google.android.material.button.MaterialButton
                 android:id="@+id/like_button"
                 style="@style/Widget.Flashat.TextButton.Small.Primary"
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content"
                 android:text="@{@plurals/chat_like_count(message.totalLikes,message.totalLikes)}"
                 android:textAppearance="@style/TextAppearance.Flashat.ChatBubble.Likes"
                 tools:text="22 Likes"
                 android:visibility="visible"
                 tools:icon="@drawable/ic_chat_like"
                 app:goneIfNot="@{message.senderDetails.premium &amp;&amp; message.reported == false}"
                 app:layout_constraintBottom_toBottomOf="@+id/comments_layout"
                 app:layout_constraintEnd_toEndOf="parent"
                 app:layout_constraintTop_toTopOf="@+id/comments_layout" />-->

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>