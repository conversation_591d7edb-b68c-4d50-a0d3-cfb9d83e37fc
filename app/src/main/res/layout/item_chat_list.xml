<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="chat" type="com.app.messej.data.model.entity.PrivateChat"/>
        <variable name="selected" type="Boolean"/>
        <variable name="currentUser" type="Integer"/>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_holder"
        android:clickable="true"
        android:background="@{selected?@color/colorSurfaceSecondaryDark:@color/transparent}"
        android:foreground="?attr/selectableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/activity_margin"
        android:paddingHorizontal="@dimen/activity_margin">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/huddle_dp"
            android:layout_width="@dimen/message_list_dp_size"
            android:layout_height="@dimen/message_list_dp_size"
            app:riv_corner_radius="@dimen/message_list_dp_size"
            android:scaleType="centerCrop"
            android:elevation="2dp"
            android:layout_marginBottom="@dimen/activity_margin"
            app:imageUrl="@{chat.receiverDetails.thumbnail}"
            app:placeholder="@{@drawable/im_user_placeholder}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/im_user_placeholder" />

        <com.makeramen.roundedimageview.RoundedImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:elevation="2dp"
            app:goneIfNot="@{selected}"
            android:animateLayoutChanges="true"
            app:layout_constraintBottom_toBottomOf="@+id/huddle_dp"
            app:layout_constraintEnd_toEndOf="@+id/huddle_dp"
            app:layout_constraintStart_toStartOf="@+id/huddle_dp"
            app:layout_constraintTop_toTopOf="@+id/huddle_dp"
            app:riv_corner_radius="@dimen/message_list_dp_size"
            tools:visibility="gone"
            android:src="@drawable/ic_list_selection_check"
            tools:riv_corner_radius="@dimen/premium_huddle_dp_radius" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="@dimen/premium_badge_size"
            android:layout_height="@dimen/premium_badge_size"
            android:elevation="4dp"
            app:userBadge="@{chat.receiverDetails.userBadge}"
            tools:src="@drawable/ic_user_badge_premium"
            app:layout_constraintStart_toStartOf="@id/huddle_dp"
            app:layout_constraintTop_toTopOf="@id/huddle_dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/huddle_name"
            style="@style/TextAppearance.Flashat.Subtitle2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            android:layout_marginEnd="@dimen/element_spacing"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{chat.receiverDetails.name}"
            app:layout_constraintBottom_toTopOf="@id/huddle_last_message"
            app:layout_constraintEnd_toStartOf="@+id/huddle_time"
            app:layout_constraintStart_toEndOf="@id/huddle_dp"
            app:layout_constraintTop_toTopOf="@id/huddle_dp"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_goneMarginEnd="@dimen/activity_margin"
            tools:text="John Doe" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/huddle_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Flashat.Label"
            android:textColor="@{chat.hasUnread?@color/colorPrimaryDark:@color/textColorSecondary}"
            tools:textColor="@color/colorPrimaryDark"
            app:layout_constraintBaseline_toBaselineOf="@id/huddle_name"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="@string/common_date_today" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/huddle_last_message_media_icon"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:adjustViewBounds="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/huddle_last_message"
            app:layout_constraintStart_toStartOf="@+id/huddle_name"
            app:layout_constraintTop_toTopOf="@+id/huddle_last_message"
            app:tint="@color/textColorSecondaryLight"
            tools:srcCompat="@drawable/ic_chat_image_14dp"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/huddle_last_message"
            style="@style/TextAppearance.Flashat.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/line_spacing"
            app:layout_goneMarginStart="0dp"
            android:layout_marginTop="@dimen/line_spacing"
            android:layout_marginEnd="@dimen/line_spacing"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@{chat.hasUnread?@color/textColorSecondary:@color/textColorSecondaryLight}"
            app:layout_constraintBottom_toBottomOf="@+id/huddle_dp"
            app:layout_constraintEnd_toStartOf="@+id/action_barrier"
            app:layout_constraintStart_toEndOf="@+id/huddle_last_message_media_icon"
            app:layout_constraintTop_toBottomOf="@+id/huddle_name"
            app:layout_goneMarginEnd="0dp"
            tools:text="Thanks! I’m happy to connect you. Please let e know"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/action_barrier"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:barrierAllowsGoneWidgets="true"
            app:barrierDirection="start"
            app:constraint_referenced_ids="huddle_unread_counter,read_receipt_icon" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/huddle_unread_counter"
            style="@style/Widget.Flashat.ChatList.UnreadCounter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:goneIfNot="@{chat.hasUnread}"
            android:text="@{``+chat.unreadCount}"
            app:layout_constraintBaseline_toBaselineOf="@id/huddle_last_message"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="3"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/read_receipt_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:baselineAlignBottom="true"
            app:goneIf="@{chat.hasUnread || chat.lastMessage == null || chat.lastMessage.sender != currentUser}"
            app:setReadReceipt="@{chat.lastMessage.messageDeliveryReadStatus}"
            app:layout_constraintBaseline_toBaselineOf="@id/huddle_last_message"
            app:layout_constraintEnd_toEndOf="parent"
            tools:src="@drawable/ic_check_all"
            tools:visibility="visible" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorDividerLight"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>