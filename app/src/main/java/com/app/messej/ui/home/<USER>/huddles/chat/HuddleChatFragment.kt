package com.app.messej.ui.home.publictab.huddles.chat

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.core.os.BundleCompat
import androidx.core.view.MenuCompat
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.paging.CombinedLoadStates
import androidx.paging.LoadState
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.EndPollDialogLayoutBinding
import com.app.messej.databinding.FragmentHuddleChatBinding
import com.app.messej.databinding.LayoutJoinErrorBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.chat.ActiveChatTracker
import com.app.messej.ui.chat.GroupChatViewModel
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.chat.delegates.GroupChatActionsDelegate
import com.app.messej.ui.chat.imageedit.PostImageEditFragment
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureHuddlePostingAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleDeleteLeaveConfirmFragment
import com.app.messej.ui.home.publictab.huddles.poll.CreatePollFragment
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentShareExtensions.getExternalShareResult
import com.app.messej.ui.utils.FragmentShareExtensions.setExternalShareRequestToHuddlePost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


open class HuddleChatFragment : BaseHuddleChatFragment(),MenuProvider, GroupChatActionsDelegate by GroupChatActionsDelegate.GroupChatActionsDelegateImpl {

    private lateinit var binding: FragmentHuddleChatBinding

    private val args: HuddleChatFragmentArgs by navArgs()

    override val viewModel: HuddleChatViewModel by navGraphViewModels(R.id.nav_chat_huddle)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_chat, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        setEmptyView()
        updateEmptyView()
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.navigationIcon = ContextCompat.getDrawable(requireContext(),R.drawable.ic_back_button_premium)
            setupActionBar(binding.customActionBar.toolbar)
        }
        viewModel.clearImageEditMode()
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.messagesList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView?
        get() = binding.multiStateView

    private fun setup() {
        viewModel.setHuddleId(args.huddleId, HuddleType.PUBLIC, args.messageId)
        ActiveChatTracker.registerActiveScreen(ActiveChatTracker.ActiveChatScreen.GroupChat(args.huddleId), viewLifecycleOwner)
        binding.customActionBar.toolbarLayout.setOnClickListener {
            val isTribe = viewModel.huddle.value?.isTribe == true
            val isParticipant = viewModel.huddle.value?.involvement == HuddleInvolvement.PARTICIPANT
            val canEnterAsManagerOrAdmin = viewModel.huddle.value?.involvement in listOf(HuddleInvolvement.MANAGER, HuddleInvolvement.ADMIN)

            val canEnterAsParticipant = if (isTribe) false else isParticipant
            val isEmpoweredToEnter = viewModel.userEmpowerment.canEnterAnyHuddle && viewModel.huddle.value?.empoweredUserBlocked == false

            val isNotBlocked = viewModel.huddleStatus.value !in GroupChatStatus.entryBlocked()

            if ((canEnterAsParticipant || canEnterAsManagerOrAdmin || isEmpoweredToEnter) && isNotBlocked) {
                findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalNavHuddleInfo(args.huddleId))
            }
        }

        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
                viewModel.refreshPoll(args.huddleId)
            }
        }

        binding.actionPoll.setOnClickListener {
            showPollMenu(it)
        }

        binding.pollViewMore.setOnClickListener {
            viewModel.poll.value?.answers?.toTypedArray()?.let {
                findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddlePollFragment(args.huddleId,viewModel.userIsManager.value?:false,viewModel.isEditMode.value?:false,viewModel.answerIndex.value?:-1))
            }

        }

        binding.pollOptionOne.radioButton.apply {
            setOnClickListener {
                if(!isSelected){
                    viewModel.setPollOptionOnSelected()
                }else{
                    viewModel.pollOptionOneUnSelected()
                }
            }
        }


        binding.pollOptionTwo.radioButton.apply {
            setOnClickListener {
                if(!isSelected){
                    viewModel.setPollOptionTwoSelected()
                }else{
                    viewModel.pollOptionTwoUnSelected()
                }
            }
        }

        viewModel.pollLoading.observe(viewLifecycleOwner){
            it?.let {
                if (it) binding.optionMultiStateView.viewState = MultiStateView.ViewState.LOADING
                else{
                    binding.optionMultiStateView.viewState = MultiStateView.ViewState.CONTENT
                }
            }
        }


        binding.pollOptionThree.radioButton.apply {
            setOnClickListener {
                if(!isSelected){
                    viewModel.setPollOptionThreeSelected()
                }else{
                    viewModel.pollOptionThreeUnSelected()
                }
            }
        }

        binding.fabNewPosts.setOnClickListener {
            binding.messagesList.scrollToPosition(0)
            mAdapter?.refresh()
        }

        getExternalShareResult { message, mediaType, fileUri ->
            if (mediaType != MediaType.DOCUMENT && mediaType != MediaType.AUDIO) {
                setExternalShareRequestToHuddlePost(message, mediaType?.code, fileUri?.toString())
                navigateToPostSheet()
            }
        }
    }

    override fun processLoadStates(loadState: CombinedLoadStates) {
        super.processLoadStates(loadState)
        if (loadState.refresh !is LoadState.Loading) {
            binding.swipeRefresh.isRefreshing = false
        }
        if (loadState.refresh is LoadState.Loading) {
            binding.swipeRefresh.isRefreshing = true
            viewModel.clearNewPosts()
        }
    }

    override val provideAdapter: ChatAdapter
        get() = HuddleChatAdapter(layoutInflater, viewModel.user.id, this, viewModel.user.citizenship)

    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {
        viewModel.enableChatInteraction.observe(viewLifecycleOwner) {
            mAdapter?.notifyDataSetChanged()
            (activity as MenuHost).invalidateMenu()
            updateEmptyView()
        }

        viewModel.huddle.observe(viewLifecycleOwner) {
            binding.customActionBar.huddle = it
        }
        viewModel.huddlePrivacy.observe(viewLifecycleOwner) {
            updateEmptyView()
        }
        viewModel.huddleStatus.observe(viewLifecycleOwner) {

            Log.d("GCVM", "observe: huddle status changed: $it")
            val type = viewModel.huddle.value?.huddleType ?: return@observe
            initJoinLayout(
                binding.actionDecorHolder, layoutInflater, viewLifecycleOwner, viewModel, it, type,
                onJoinClick = {
                    ensureInteractionAllowed {
                        viewModel.instantJoinHuddle()
                    }
                }, onAcceptInviteClick = {
                    viewModel.acceptInvite()
                }
            )
            (activity as MenuHost).invalidateMenu()
        }

        viewModel.adminStatus.observe(viewLifecycleOwner) {
            it ?: return@observe
            Log.d("GCVM", "observe: huddle admin status changed: $it")
            initAdminInvite(binding.actionDecorHolderTop, layoutInflater, viewLifecycleOwner, viewModel, it)
        }

        viewModel.userHasElevatedRole.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.canShareHuddleLink.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.canSendInvites.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.userIsManager.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }

        viewModel.canForwardSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_forward)?.isVisible = it
        }

        viewModel.canReplyToSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_reply)?.isVisible = it
        }

        viewModel.canReportSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_report)?.isVisible = it
        }

        viewModel.onlineCount.observe(viewLifecycleOwner) {
            binding.customActionBar.onlineCount = it
        }

        viewModel.pinnedPost.observe(viewLifecycleOwner) {
            initHuddlePinnedPost(binding.actionDecorPinnedPost, layoutInflater, viewLifecycleOwner, viewModel)
        }

        viewModel.answerIndex.observe(viewLifecycleOwner){
            it?.let {
                viewModel.setPollOptions(it)
            }
        }

        viewModel.onExitHuddle.observe(viewLifecycleOwner){
            findNavController().popBackStack()
        }


        viewModel.onExitChat.observe(viewLifecycleOwner) { reason ->
            when (reason) {
                GroupChatViewModel.ChatExitReason.HuddleDeleted -> Toast.makeText(context, getString(R.string.huddle_does_not_exist), Toast.LENGTH_SHORT).show()
                GroupChatViewModel.ChatExitReason.LoadFailed -> Toast.makeText(context, getString(R.string.huddle_load_failed), Toast.LENGTH_SHORT).show()
                is GroupChatViewModel.ChatExitReason.WrongHuddleType -> Toast.makeText(context, getString(R.string.huddle_wrong_type), Toast.LENGTH_SHORT).show()
                is GroupChatViewModel.ChatExitReason.UserAction -> when (reason.action) {
                    AbstractHuddle.HuddleUserStatus.INVITE_DECLINED -> {
//                        Toast.makeText(requireContext(), "Invite Declined", Toast.LENGTH_SHORT).show()
                    }

                    AbstractHuddle.HuddleUserStatus.JOIN_REQUEST_BLOCKED -> {
                        Toast.makeText(requireContext(), resources.getString(R.string.huddle_group_invite_blocked), Toast.LENGTH_SHORT).show()
                    }

                    AbstractHuddle.HuddleUserStatus.CANCELLED -> {
                        Toast.makeText(requireContext(), resources.getString(R.string.huddle_group_request_cancelled), Toast.LENGTH_SHORT).show()
                    }

                    else -> {}
                }

                is GroupChatViewModel.ChatExitReason.HuddleAction -> {}
                else -> {}
            }
            findNavController().popBackStack()
        }

        viewModel.canDeleteSelectionForEveryone.observe(viewLifecycleOwner) {
            if (it) 1 else 0
        }

        viewModel.newPostCounter.observe(viewLifecycleOwner) {
            if (it > 0) {
                binding.fabNewPosts.text = getString(R.string.title_new_post, it)
                lifecycleScope.launch {
                    binding.fabNewPosts.show()
                    delay(1000)
                    if (!isActive) return@launch
                    binding.fabNewPosts.extend()
                }
            } else {
                binding.fabNewPosts.hide()
            }
        }

        viewModel.onAddChatReplyTo.observe(viewLifecycleOwner) {
            if (it) {
                navigateToPostSheet()
            }
        }

        viewModel.onFailedToAcceptAdminInvite.observe(viewLifecycleOwner) {
            if (it) {
                showSnackbar(R.string.huddle_admin_request_limit_exceed_toast_receiver)
            }
        }

        viewModel.onHuddleLeft.observe(viewLifecycleOwner) {
            if (it) findNavController().popBackStack()
        }

        viewModel.pollOptionError.observe(viewLifecycleOwner){
            it?.let {
                Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
            }
        }
        viewModel.isPollVotedSuccess.observe(viewLifecycleOwner){
           if (it) {
               Toast.makeText(requireContext(), getString(R.string.title_poll_responese), Toast.LENGTH_SHORT).show()
               mAdapter?.refresh()
           }
        }

        viewModel.pollCreated.observe(viewLifecycleOwner){
            mAdapter?.refresh()
        }

        viewModel.pollEnabled.observe(viewLifecycleOwner){
          it?.let {
              if(it){
                  mAdapter?.refresh()
              }
          }
        }

        viewModel.isEndPollSuccess.observe(viewLifecycleOwner){
            if (it) Toast.makeText(requireContext(), getString(R.string.poll_end_toast), Toast.LENGTH_SHORT).show()
            binding.messagesList.scrollToPosition(0)
            mAdapter?.refresh()
        }
        viewModel.onJoinLimitExceeded.observe(viewLifecycleOwner) { params ->
            MaterialDialog(requireContext()).show {
                val view = DataBindingUtil.inflate<LayoutJoinErrorBinding>(layoutInflater, R.layout.layout_join_error, null, false)
                customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                cancelable(true)
                view.txtContent.text = context.getString(if (params.premium) R.string.title_huddle_max_count_premium_user else R.string.title_huddle_max_count_free_user, params.limit)
                view.closeButton.setOnClickListener {
                    dismiss()
                    findNavController().popBackStack()
                }
            }
        }

        viewModel.onJoinActionError.observe(viewLifecycleOwner) { message ->
            MaterialDialog(requireContext()).show {
                val view = DataBindingUtil.inflate<LayoutJoinErrorBinding>(layoutInflater, R.layout.layout_join_error, null, false)
                customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                cancelable(true)
                view.txtContent.text = message
                view.closeButton.setOnClickListener {
                    dismiss()
                    findNavController().popBackStack()
                }
            }
        }

        viewModel.onAudioLessThanASecond.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.chat_mic_hold_message), Toast.LENGTH_LONG).show()
        }

        viewModel.newActivityReceived.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }

        setFragmentResultListener(HuddleDeleteLeaveConfirmFragment.HUDDLE_DELETE_LEAVE_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(HuddleDeleteLeaveConfirmFragment.HUDDLE_DELETE_LEAVE_RESULT_KEY)
            if (result) viewModel.leaveHuddle()
        }

        setFragmentResultListener(CreatePollFragment.POLL_CREATE_MODE){ _, bundle->
            val isPollCreated = bundle.getBoolean(CreatePollFragment.POLL_RESULT_SUCCESS)
            if (isPollCreated) {
                viewModel.setPollCreated()
            }
        }
        setFragmentResultListener(PostImageEditFragment.IMAGE_SAVE_REQUEST_KEY) { _, bundle ->
            viewModel.clearImageEditMode()
            val resultUri = BundleCompat.getParcelable(bundle, PostImageEditFragment.IMAGE_SAVE_RESULT_KEY, Uri::class.java)
            viewModel.addImage(resultUri!!)
            navigateToPostSheet()
        }

        viewModel.onNavigateToPostComments.observe(viewLifecycleOwner) {
            findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddlePostCommentsFragment(it, args.huddleId))
        }

        viewModel.videoPostedToFlash.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.huddle_to_flash_video_posted), Toast.LENGTH_SHORT).show()
            }
        }
    }

    private var emptyViewBinding: LayoutListStateEmptyBinding? = null

    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            Log.w("BCDFLSL", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("BCDFLSL", "setEmptyView: ")
        }
    }


    private fun updateEmptyView() {
        Log.w("BCDFLSL", "updateEmptyView: $emptyViewBinding")
        emptyViewBinding?.apply {
            prepare(
                image = if(viewModel.huddleStatus.value == GroupChatStatus.RESTRICTED || viewModel.huddleStatus.value == GroupChatStatus.BLOCKED || viewModel.huddleStatus.value == GroupChatStatus.ADMIN_BLOCKED) null else if(viewModel.huddle.value?.isTribe == true) R.drawable.ic_tribe_empty  else R.drawable.im_eds_huddles,
                message = if(viewModel.huddleStatus.value == GroupChatStatus.RESTRICTED || viewModel.huddleStatus.value == GroupChatStatus.BLOCKED || viewModel.huddleStatus.value == GroupChatStatus.ADMIN_BLOCKED) null else if(viewModel.huddle.value?.isTribe == true && viewModel.huddle.value?.totalMembers!! <=1 ){
                    R.string.chat_tribe_post_title
                }
                else R.string.chat_huddle_eds,
                action = if (viewModel.huddlePrivacy.value?.canPost == true && viewModel.enableChatInteraction.value == true) {
                    if(viewModel.huddle.value?.isTribe == true) R.string.title_tribe_invite_action_title else R.string.chat_huddle_post_create_title
                } else null
            ) {
                if (viewModel.huddle.value?.isTribe==true) shareTribe() else navigateToPostSheet()
            }
        }
    }




    private fun shareTribe() {
        val inviteText =  resources.getString(R.string.tribe_share_text, viewModel.user.username,viewModel.user.profile.referralLink)
        if (inviteText.isNotEmpty()) {
            val sendIntent: Intent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_TEXT, inviteText)
                type = "text/plain"
            }
            val shareIntent = Intent.createChooser(sendIntent, null)
            startActivity(shareIntent)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_chat_huddles_actions, menu)
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
//        menu.findItem(R.id.action_new_post).isEnabled = (viewModel.huddle.value?.privacy?.canPost == true)
        menu.findItem(R.id.action_new_post).isVisible =  (viewModel.enableChatInteraction.value == true)

        menu.findItem(R.id.action_search).isVisible = viewModel.userEmpowerment.canBlockAnyUserInHuddle || (viewModel.huddleStatus.value == GroupChatStatus.BLOCKED ||
                viewModel.huddleStatus.value == GroupChatStatus.INVITED || viewModel.huddleStatus.value == GroupChatStatus.OPEN_TO_JOIN
                || viewModel.huddleStatus.value == GroupChatStatus.REQUEST_TO_JOIN || viewModel.huddleStatus.value == GroupChatStatus.JOIN_REQUESTED) == false || viewModel.huddleStatus.value == GroupChatStatus.ADMIN_BLOCKED == false
        val requestsIsVisible = viewModel.userHasElevatedRole.value==true && viewModel.huddle.value?.isTribe==false && !viewModel.entryBlocked()
        val inviteIsVisible = viewModel.canSendInvites.value==true  && viewModel.huddle.value?.isTribe==false && !viewModel.entryBlocked()
        val shareIsVisible =viewModel.canShareHuddleLink.value==true && viewModel.huddle.value?.isTribe==false && !viewModel.entryBlocked()
        val leaveIsVisible = viewModel.userHasElevatedRole.value == false && viewModel.huddle.value?.isTribe==false
        val privacyIsVisible = viewModel.userIsManager.value==true && !viewModel.entryBlocked()
        menu.findItem(R.id.action_more).isVisible = (requestsIsVisible || inviteIsVisible || shareIsVisible || leaveIsVisible || privacyIsVisible) && !viewModel.entryBlocked()
        if(viewModel.userHasElevatedRole.value == true) {
            notificationBadge = BadgeDrawable.create(requireContext()).apply {
                isVisible = (viewModel.huddle.value?.requestsAndInvites ?: 0) > 0
                adjustForNotifications(requireContext())
                BadgeUtils.attachBadgeDrawable(this, binding.customActionBar.toolbar, R.id.action_more)
            }
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> navigateToSearch()
            R.id.action_more -> showMoreMenu(menuItem)
            R.id.action_new_post -> {
                if (viewModel.huddle.value?.privacy?.canPost == true) navigateToPostSheet()
                else showSnackbar(R.string.huddle_post_restriction_message)
            }
            else -> return false
        }
        return true
    }

    private fun navigateToSearch() {
        findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddleMessagesSearchFragment(args.huddleId))
    }
    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        MenuCompat.setGroupDividerEnabled(popup.menu, true)
        popup.menuInflater.inflate(R.menu.menu_chat_huddles_action_more, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.action_requests).isVisible = viewModel.userHasElevatedRole.value==true && viewModel.huddle.value?.isTribe==false && !viewModel.entryBlocked()
            findItem(R.id.action_invite).isVisible = viewModel.canSendInvites.value==true  && viewModel.huddle.value?.isTribe==false && !viewModel.entryBlocked()
            findItem(R.id.action_share).isVisible =viewModel.canShareHuddleLink.value==true && viewModel.huddle.value?.isTribe==false && !viewModel.entryBlocked()
            findItem(R.id.action_leave).isVisible = viewModel.userHasElevatedRole.value == false && viewModel.huddle.value?.isTribe==false && viewModel.huddle.value?.huddleStatus == GroupChatStatus.ACTIVE
            findItem(R.id.action_privacy).isVisible = (viewModel.userIsManager.value==true && viewModel.user.premium)
            findItem(R.id.action_schedule_poll).isVisible = viewModel.userIsManager.value==true && viewModel.huddleStatus.value==GroupChatStatus.ACTIVE && viewModel.user.premium==true
            findItem(R.id.action_create_poll).isVisible = viewModel.userIsManager.value==true && viewModel.huddleStatus.value==GroupChatStatus.ACTIVE && viewModel.user.premium==true
            findItem(R.id.action_past_poll).isVisible = viewModel.huddleStatus.value == GroupChatStatus.ACTIVE && (viewModel.user.premium || (!viewModel.user.premium && viewModel.userIsManager.value == false))
            findItem(R.id.action_removed_dears).isVisible = viewModel.huddle.value?.isTribe == true
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_requests -> findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalManageRequestInviteFragment(args.huddleId))
                R.id.action_invite -> findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalAddParticipantsFragment(args.huddleId, false))
                R.id.action_leave -> findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalHuddleDeleteLeaveConfirmFragment(false, HuddleType.PUBLIC))
                R.id.action_privacy->findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddlePrivacy(args.huddleId,viewModel.huddle.value?.isTribe?:false))
                R.id.action_share -> {
                    val inviteText = if (viewModel.huddle.value!!.isPrivate) resources.getString(R.string.huddle_info_share_text_group,viewModel.huddle.value?.name, viewModel.huddle.value?.inviteLink)
                    else resources.getString(R.string.huddle_info_share_text, viewModel.huddle.value?.name, viewModel.huddle.value?.inviteLink)
                    if (inviteText.isNotEmpty()) {
                        val sendIntent: Intent = Intent().apply {
                            action = Intent.ACTION_SEND
                            putExtra(Intent.EXTRA_TEXT, inviteText)
                            type = "text/plain"
                        }
                        val shareIntent = Intent.createChooser(sendIntent, null)
                        startActivity(shareIntent)
                    }
                }
                R.id.action_past_poll->{
                    findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToSchedulePollFragment(args.huddleId,viewModel.userIsManager.value?:false,PollType.PAST_POLL,))
                }

                R.id.action_schedule_poll->{
                    findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToSchedulePollFragment(args.huddleId,viewModel.userIsManager.value?:false,PollType.SCHEDULE_POLL))
                }
                R.id.action_create_poll -> {
                    ensureHuddlePostingAllowed {
                        findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToCreatePoll(args.huddleId))
                    }
                }
                R.id.action_removed_dears -> {
                    findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToRemoveDearsFragment(args.huddleId))
                }

                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }

        popup.show()
    }

    private fun confirmEndPoll() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<EndPollDialogLayoutBinding>(layoutInflater, R.layout.end_poll_dialog_layout, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.nickNameTitle.text = getString(R.string.poll_end_title)
            view.actionYes.setOnClickListener {
                viewModel.endPoll(viewModel.poll.value?.id?:-1,args.huddleId)
                dismiss()
            }
            view.actionCancel.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun showPollMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.menu_chat_poll_actions, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.action_result_details).isVisible =viewModel.userIsManager.value==true && viewModel.isPollHide.value==false
            findItem(R.id.action_end_poll).isVisible =viewModel.userIsManager.value==true && viewModel.isPollHide.value==false
            findItem(R.id.action_hide_poll).apply {
                title = getString(if (viewModel.isPollHide.value == true) R.string.un_hide_poll else R.string.poll_hide_poll)
            }
            findItem(R.id.action_send_invitations).isVisible =viewModel.userIsManager.value==true && viewModel.isPollHide.value==false
            findItem(R.id.action_edit_answer).isVisible = viewModel.isPollHide.value==false && viewModel.poll.value?.userAnswer!=null
//            findItem(R.id.action_send_invitations).isVisible =false
            findItem(R.id.action_hide_poll).isVisible =  viewModel.huddleStatus.value==GroupChatStatus.ACTIVE
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_end_poll -> {
                        confirmEndPoll()
                }

                R.id.action_result_details -> {
                    findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToPollResultFragment(viewModel.poll.value?.id!!))
                }

                R.id.action_send_invitations->{
                    findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToSendPollInvitationFragment(viewModel.poll.value?.id!!,
                                                                                                                                             viewModel.huddle.value?.isTribe!!
                    ))
                }
                R.id.action_hide_poll -> {
                    if (viewModel.isPollHide.value == true) {
                        viewModel.togglePollVisibility(false)
                    } else {
                        viewModel.togglePollVisibility(true)
                    }
                }

                R.id.action_edit_answer -> {
                    findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddlePollFragment(viewModel.huddle.value?.id!!,viewModel.userIsManager.value!!,true,viewModel.answerIndex.value!!))
                }

                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }

        popup.show()
    }



    override fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_chat_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_reply -> viewModel.replyToSelection()
                        R.id.action_copy -> viewModel.copySelection()
                        R.id.action_report -> viewModel.reportSelection()
                        R.id.action_forward->{
                             findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.HUDDLES,messageId = viewModel.selectedChats.value?.get(0)))
                        }
                        R.id.action_delete -> confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message,false,
                        viewModel.canDeleteSelectionForEveryone.value == true) {
                            viewModel.deleteSelection(it)
                        }
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    override fun navigateToPostSheet() {
        ensureHuddlePostingAllowed {
            findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddlePostFragment())
        }
    }

    override fun onItemReply(item: AbstractChatMessage, position: Int) {
        //Disable when "chatSelectionMode" is ON
        if (viewModel.chatSelectionMode.value == true) return
        //add below check groupcheck status
        if (!viewModel.canReplyToPost(item)) showSnackbar(R.string.huddle_reply_restriction_message)
        else if(viewModel.huddleStatus.value!=GroupChatStatus.ACTIVE) showSnackbar(R.string.huddle_reply_not_joined_message)
        else viewModel.replyToSelection(item)
    }

    override fun onClickOnComments(item: HuddleChatMessage, position: Int) {
        //Disable when "chatSelectionMode" is ON
        if (viewModel.chatSelectionMode.value == true) return
        if (item.senderDetails?.blockedByEitherAdmin == false) {
            findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddlePostCommentsFragment(item.messageId, args.huddleId))
        }
    }

    override fun onClickOnMention(user: MentionedUser) {
        //Disable when "chatSelectionMode" is ON
        if (viewModel.chatSelectionMode.value == true) return
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(user.id))
    }

    override fun onGiftClick(msg: HuddleChatMessage) {
        if(viewModel.huddleStatus.value==GroupChatStatus.ACTIVE ) {
            viewModel.huddle.value?.managerId?.let {
                findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddleGiftListFragment(msg.sender, msg.huddleIdInt, singleTabItem = GiftContext.GIFT_HUDDLE, msg.messageId, managerId =it))
            }
        }
    }

    override fun goToGiftFile(isSelf: Boolean) {
        if(isSelf)
            findNavController().navigateSafe(HuddleChatFragmentDirections.actionPublicHuddleChatFragmentToHuddleGiftFragment())
    }

    override fun goToIdCard(msg: HuddleChatMessage) {
        findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalPublicUserProfileFragment(msg.sender,false))
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.sendChatExit()
    }
}

