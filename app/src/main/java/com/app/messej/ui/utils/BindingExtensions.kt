package com.app.messej.ui.utils

import android.content.Context
import android.graphics.Typeface
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import android.widget.Toast
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.view.MenuCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.enums.AttachDocumentType
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.HelpSettingsItem
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.StateAffairsTypes
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.data.utils.MediaUtils
import com.app.messej.databinding.ItemAffairUserLayoutBinding
import com.app.messej.databinding.ItemChatMessageReplyToBinding
import com.app.messej.databinding.ItemUserIdCardCommonViewBinding
import com.app.messej.databinding.LayoutIdCardToolTipInformationBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutListStateErrorBinding
import com.app.messej.databinding.LayoutNicknameIdCardEditBinding
import com.app.messej.databinding.LayoutStateAffairLowGradedUsersBinding
import com.app.messej.ui.chat.adapter.ChatMessageViewHolder
import com.app.messej.ui.home.publictab.authorities.stateAffairs.StateAffairsTotalUsers
import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.app.messej.ui.profile.PublicUserProfileFragmentArgs
import com.app.messej.ui.profile.PublicUserProfileViewModel
import com.app.messej.ui.utils.EnumUtils.displayText
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.app.messej.ui.utils.TextFormatUtils.setFormattedText
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonSizeSpec
import com.skydoves.balloon.createBalloon
import com.stfalcon.imageviewer.StfalconImageViewer


object BindingExtensions {
    fun LayoutListStateEmptyBinding.prepare(@DrawableRes image: Int? = null, @StringRes message: Int?, @StringRes action: Int? = null, onClick: View.OnClickListener? = null) {
        if (image != null) {
            edsEmptyImage.setImageResource(image)
        }
        edsEmptyImage.isVisible = image != null
        edsEmptyMessage.isVisible = message != null
        if (message != null) {
            edsEmptyMessage.setText(message)
        }
        edsEmptyAction.isVisible = action != null
        action?.let {
            edsEmptyAction.setText(action)
        }
        onClick?.let {
            edsEmptyAction.setOnClickListener(onClick)
        }
    }

    fun LayoutListStateErrorBinding.prepare(@DrawableRes image: Int? = null, @StringRes message: Int?, @StringRes action: Int? = null, onClick: View.OnClickListener? = null) {
        if (image != null) {
            edsErrorImage.setImageResource(image)
        }
        edsErrorImage.isVisible = image != null
        edsErrorMessage.isVisible = message != null
        if (message != null) {
            edsErrorMessage.setText(message)
        }
        edsErrorAction.isVisible = action != null
        action?.let {
            edsErrorAction.setText(action)
        }
        onClick?.let {
            edsErrorAction.setOnClickListener(onClick)
        }
    }

    fun ItemChatMessageReplyToBinding.prepare(reply: ReplyTo, @ColorRes bubbleColor: Int = R.color.colorChatDefault) {
        replyDecor.setBackgroundColor(ContextCompat.getColor(root.context, ChatMessageViewHolder.getChatHighlightDarkColor(bubbleColor)))
        replyLayout.setCardBackgroundColor(ContextCompat.getColor(root.context, ChatMessageViewHolder.getChatHighlightColor(bubbleColor)))
        Log.w("ICMRTB", "prepare: ${reply.messageType}")
        when (reply.messageType) {
            AbstractChatMessage.MessageType.TEXT -> {
                replyMediaIcon.visibility = View.GONE
//                replyMessage.text = reply.message
                replyMessage.setFormattedText(reply.message ?: "", reply.chatTextColor)

            }

            AbstractChatMessage.MessageType.MEDIA -> {
                when (reply.mediaType) {
                    MediaType.IMAGE -> {
                        replyMediaIcon.visibility = View.VISIBLE
                        replyMediaIcon.setImageDrawable(ContextCompat.getDrawable(root.context, R.drawable.ic_chat_image_14dp))


                        val message = if (!reply.message.isNullOrBlank()) reply.message else root.context.getString(R.string.message_list_preview_photo)
                        replyMessage.setFormattedText(message ?: "", reply.chatTextColor)
                    }

                    MediaType.AUDIO -> {
                        replyMediaIcon.visibility = View.VISIBLE
                        replyMediaIcon.setImageDrawable(ContextCompat.getDrawable(root.context, R.drawable.ic_chat_record))

                        val message = if (!reply.message.isNullOrBlank()) reply.message else root.context.getString(R.string.message_list_preview_audio, reply.mediaDuration)
                        replyMessage.setFormattedText(message ?: "", reply.chatTextColor)
                    }

                    MediaType.VIDEO -> {
                        replyMediaIcon.visibility = View.VISIBLE
                        replyMediaIcon.setImageDrawable(ContextCompat.getDrawable(root.context, R.drawable.ic_chat_video_14dp))

                        val message = if (!reply.message.isNullOrBlank()) reply.message else root.context.getString(R.string.message_list_preview_video, reply.mediaDuration)
                        replyMessage.setFormattedText(message ?: "", reply.chatTextColor)
                    }

                    MediaType.DOCUMENT -> {
                        replyMediaIcon.visibility = View.VISIBLE
                        replyMediaIcon.setImageDrawable(ContextCompat.getDrawable(root.context, MediaUtils.getDocumentResFromType(AttachDocumentType.forExtension(reply.media!!.split(".").last()))))

                        replyMessage.setFormattedText(reply.mediaName.orEmpty(), reply.chatTextColor)
                    }

                    else -> {}
                }
            }

            AbstractChatMessage.MessageType.LOCATION -> {
                Log.w("ICMRTB", "prepare: showing loc reply")
                replyMediaIcon.visibility = View.VISIBLE
                replyMediaIcon.setImageDrawable(ContextCompat.getDrawable(root.context, R.drawable.ic_chat_location_14dp))
                replyMessage.text = root.context.getString(R.string.message_list_preview_location)
            }

            AbstractChatMessage.MessageType.STICKER -> {
                replyMediaIcon.visibility = View.VISIBLE
                replyMediaIcon.setImageDrawable(ContextCompat.getDrawable(root.context, R.drawable.ic_sticker_comment))
                replyMessage.text = root.context.getString(R.string.chat_sticker)
            }

            else -> {}
        }
        if (reply.reported) {
            replyMediaIcon.visibility = View.GONE
            replyMessage.setText(R.string.chat_message_reported)
        } else if (reply.deleted) {
            replyMediaIcon.visibility = View.GONE
            replyMessage.setText(R.string.chat_message_deleted)
        }
    }

    fun ItemUserIdCardCommonViewBinding.setupIDCard(
        profile: OtherUser?,
        context: Context,
        lifecycleOwner: LifecycleOwner,
        publicUserProfileArgs: PublicUserProfileFragmentArgs,
        viewModel: PublicUserProfileViewModel,
        isUserStrengthViewHidden: Boolean = false,
        presidentIdCardBackground: Int? = null,
        isIdCardTitleBackgroundVisible: Boolean = true,
        onNavigateToDears: (() -> Unit)? = null,
        onNavigateToFans: (() -> Unit)? = null,
        onNavigateToLiker: (() -> Unit)? = null,
        onNavigateToStars: (() -> Unit)? = null,
        onNavigateToOthers: (() -> Unit)? = null,
        onNavigateToSendGift: (() -> Unit)? = null,
        onNavigateToSendFlix: (() -> Unit)? = null,
        onNavigateToRestrict: (() -> Unit)? = null,
        onNavigateToReport: (() -> Unit)? = null,
        onNavigateToBan: (() -> Unit)? = null
    ) {
        if (profile == null) return
        this.viewModel = viewModel
        val tribeCount = profile.tribeParticipantsCount
        val count = context.getString(R.string.id_card_tribe_count, tribeCount.toString())
        textTribeCount.text = count.highlightOccurrences(tribeCount.toString()) {
            StyleSpan(Typeface.BOLD)
        }
        this.isUserStrengthViewHidden = isUserStrengthViewHidden
        userType = context.resources.getString(profile.citizenship?.displayText() ?: 0)

        when (profile.citizenship) {
            UserCitizenship.CITIZEN -> {
                usertypeColor = ContextCompat.getColor(context, R.color.colorPrimary)
                idCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_citizen)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_citizen)
                textIdCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_citizen_ambassador)
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_citizen_ambassador)
                idColor = ContextCompat.getColor(context, R.color.colorHuddleTagCitizen)
                textColor = ContextCompat.getColor(context, R.color.colorPrimary)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.colorPrimary)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorSecondaryLight)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.colorIdCardUserStrength)
                tribeDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_tribe_citizen_count)
                haveTribe = true
            }

            UserCitizenship.OFFICER -> {
                usertypeColor = ContextCompat.getColor(context, R.color.colorPrimary)
                idCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_officer)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_officer)
                textIdCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_citizen_ambassador)
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_citizen_ambassador)
                idColor = ContextCompat.getColor(context, R.color.colorHuddleTagCitizen)
                textColor = ContextCompat.getColor(context, R.color.colorPrimary)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.colorPrimary)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorSecondaryLight)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.colorIdCardUserStrength)
                tribeDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_tribe_officer_count)
                haveTribe = true
            }

            UserCitizenship.AMBASSADOR -> {
                usertypeColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                idCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_ambassador)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_ambassador)
                textIdCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_citizen_ambassador)
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_citizen_ambassador)
                idColor = ContextCompat.getColor(context, R.color.colorHuddleTagCitizen)
                textColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorSecondaryLight)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.colorIdCardUserStrength)
                tribeDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_tribe_ambassador_count)
                haveTribe = true
            }

            UserCitizenship.MINISTER -> {
                usertypeColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                idCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_leader)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_leader)
                textIdCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_leader)
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_leader)
                idColor = ContextCompat.getColor(context, R.color.colorPrimary)
                textColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.colorPrimary)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.colorIdCardUserStrength)
                tribeDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_tribe_leader_count)
                haveTribe = true
            }

            UserCitizenship.PRESIDENT -> {
                usertypeColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                idCardDrawBackground = ContextCompat.getDrawable(context,presidentIdCardBackground?: R.drawable.bg_idcard_president)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_president)
                textIdCardDrawBackground = if(isIdCardTitleBackgroundVisible) ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_president) else null
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_about_president_card)
                idColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                textColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                labelTextColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                userIconTint = ContextCompat.getColor(context, R.color.colorAlwaysLightPrimary)
                iconTintColor = ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                subTitleColor = ContextCompat.getColor(context, R.color.colorPresidentIDText)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.colorAlwaysLightPrimary)
                tribeDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_tribe_president_count)
                haveTribe = true
                isPresident = true
                val layoutParams = textIdCard.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.setMargins(16, 30, 0, 0)
                textIdCard.layoutParams = layoutParams
                layoutHeader.setPadding(48, 32, 60, 40)
                textCitizenship.setPadding(0, 0, 16, 0)
                layoutUserStrength.setPadding(40, 32, 40, 32)
                cardViewItems.radius = 0F
                textTribeCount.setTypeface(textTribeCount.typeface, Typeface.BOLD)
                textTribeCount.textSize = 13F
                textTribeCount.text = count.highlightOccurrences(tribeCount.toString()) {
                    ForegroundColorSpan(
                        ContextCompat.getColor(context, R.color.colorPrimaryColorDarkest1)
                    )
                }
                val elementSpacing = context.resources.getDimensionPixelOffset(R.dimen.element_spacing)
                val layoutParamsTextViewStrength = textStrength.layoutParams as ViewGroup.MarginLayoutParams
                layoutParamsTextViewStrength.marginStart = elementSpacing
                textStrength.layoutParams = layoutParamsTextViewStrength

                val layoutParamsTextViewTribe = textTribeCount.layoutParams as ViewGroup.MarginLayoutParams
                layoutParamsTextViewTribe.marginEnd = elementSpacing
                textTribeCount.layoutParams = layoutParamsTextViewTribe
            }

            UserCitizenship.GOLDEN -> {
                usertypeColor = ContextCompat.getColor(context, R.color.white)
                idCardDrawBackground = ContextCompat.getDrawable(context,presidentIdCardBackground?: R.drawable.bg_idcard_golden)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_golden)
                textIdCardDrawBackground = if(isIdCardTitleBackgroundVisible) ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_golden) else null
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_golden)
                idColor = ContextCompat.getColor(context, R.color.white)
                textColor = ContextCompat.getColor(context, R.color.colorFlashatGolden)
                labelTextColor = ContextCompat.getColor(context, R.color.colorFlashatGolden)
                userIconTint = ContextCompat.getColor(context, R.color.colorFlashatGolden)
                iconTintColor = ContextCompat.getColor(context, R.color.colorFlashatGolden)
                subTitleColor = ContextCompat.getColor(context, R.color.white)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.colorAlwaysLightPrimary)
                tribeDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_tribe_president_count)
                haveTribe = true
                isPresident = true
                val layoutParams = textIdCard.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.setMargins(16, 30, 0, 0)
                textIdCard.layoutParams = layoutParams
                layoutHeader.setPadding(48, 32, 60, 40)
                textCitizenship.setPadding(0, 0, 16, 0)
                layoutUserStrength.setPadding(40, 32, 40, 32)
                cardViewItems.radius = 0F
                textTribeCount.setTypeface(textTribeCount.typeface, Typeface.BOLD)
                textTribeCount.textSize = 13F
                textTribeCount.text = count.highlightOccurrences(tribeCount.toString()) {
                    ForegroundColorSpan(
                        ContextCompat.getColor(context, R.color.colorAlwaysLightSecondaryLightest)
                    )
                }
                val elementSpacing = context.resources.getDimensionPixelOffset(R.dimen.element_spacing)
                val layoutParamsTextViewStrength = textStrength.layoutParams as ViewGroup.MarginLayoutParams
                layoutParamsTextViewStrength.marginStart = elementSpacing
                textStrength.layoutParams = layoutParamsTextViewStrength

                val layoutParamsTextViewTribe = textTribeCount.layoutParams as ViewGroup.MarginLayoutParams
                layoutParamsTextViewTribe.marginEnd = elementSpacing
                textTribeCount.layoutParams = layoutParamsTextViewTribe
            }

            UserCitizenship.RESIDENT -> {
                usertypeColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                idCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_resident)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_resident)
                textIdCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_resident_latest)
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_resident_latest)
                idColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                textColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                isResident = true
                userIconTextTint = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                haveTribe = false
            }

            UserCitizenship.VISITOR -> {
                usertypeColor = ContextCompat.getColor(context, R.color.textColorAlwaysLightPrimary)
                idCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_visitor)
                idCardUserStrengthDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_idcard_visitor)
                textIdCardDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_visitor)
                textAboutDrawBackground = ContextCompat.getDrawable(context, R.drawable.bg_label_idcard_visitor)
                idColor = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                textColor = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                isResident = true
                userIconTextTint = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                haveTribe = false
            }

            else -> {
                usertypeColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                idColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                textColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                labelTextColor = textColor
                userIconTint = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                iconTintColor = ContextCompat.getColor(context, R.color.textColorOnPrimary)
                isResident = false
                userIconTextTint = ContextCompat.getColor(context, R.color.textColorOnSecondary)
                haveTribe = false
            }
        }

        viewModel.chatStatus.observe(lifecycleOwner) {
            Log.d(Constants.FLASHAT_TAG, "chatStatus: $it")
        }

        viewModel.nickNameOrName.observe(lifecycleOwner) {
            Log.d(Constants.FLASHAT_TAG, "usernameOrNickName: $it")
        }

        viewModel._countryList.observe(lifecycleOwner) {
            Log.d("CountryFlag", "countryFlag: ${profile.countryCode}")
            viewModel.getFlag(it, profile.countryCode)
            idCardFlag.setImageResource(viewModel.countryFlag ?: 0)
        }

        viewModel.accountDetails.observe(lifecycleOwner) {
            idCardFlag.visibility = if (it?.showCountryFlag == true) View.VISIBLE else View.INVISIBLE
        }

        btnIdCardPopup.setOnClickListener { view ->
            idCardPopUpClick(
                viewModel = viewModel,
                context = context,
                view = view,
                onNavigateToSendGift = onNavigateToSendGift,
                onNavigateToSendFlix = onNavigateToSendFlix,
                onNavigateToRestrict = onNavigateToRestrict,
                onNavigateToReport = onNavigateToReport,
                onNavigateToBan = onNavigateToBan,
                args = publicUserProfileArgs
            )
        }

        idCardDp.setOnClickListener {
            viewModel.profile.value?.thumbnail?.let { media ->
                StfalconImageViewer.Builder(context, listOf(media)) { imageView, image ->
                    Glide.with(context).load(image).into(imageView)
                }.withTransitionFrom(idCardDp).withHiddenStatusBar(false).build().show()
            }
        }

        layoutPl.setOnClickListener {
            setupIDCardAlertView(context = context, title = R.string.id_card_skill_desc)
        }

        layoutLv.setOnClickListener {
            setupIDCardAlertView(context = context, title = R.string.id_card_generosity_desc)
        }

        layoutDears.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                onNavigateToDears?.let { it() }
            } else {
                setupIDCardAlertView(context = context, title = R.string.id_card_dears_desc)
            }
        }

        layoutFans.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                onNavigateToFans?.let { it() }
            } else {
                setupIDCardAlertView(context = context, title = R.string.id_card_stars_desc)
            }
        }

        layoutLikers.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                onNavigateToLiker?.let { it() }
            } else {
                setupIDCardAlertView(context = context, title = R.string.id_card_likers_desc)
            }
        }

        layoutStars.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                onNavigateToStars?.let { it() }
            } else {
                setupIDCardAlertView(context = context, title = R.string.id_card_stars_desc)
            }
        }

        layoutOthers.setOnClickListener {
            onNavigateToOthers?.let { it() }
        }

        textCitizenship.setOnClickListener {
            val balloon = createIDCardTooltipBalloon(context, viewModel.user.citizenship, viewModel = viewModel)
            balloon.showAlignBottom(textCitizenship)
        }
    }

    private fun setupIDCardAlertView(
        context: Context,
        @StringRes title: Int,
    ) {
        MaterialDialog(context).show {
            val view = DataBindingUtil.inflate<LayoutIdCardToolTipInformationBinding>(layoutInflater, R.layout.layout_id_card_tool_tip_information, null, false)
            view.textHeader = context.getString(title)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
            view.actionCloseOthers.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun createIDCardTooltipBalloon(
        context: Context,
        citizenship: UserCitizenship,
        viewModel: PublicUserProfileViewModel,
    ): Balloon {
        return createBalloon(context) {
            setHeight(BalloonSizeSpec.WRAP)
            setLayout(R.layout.layout_id_card_citizenship_tool_tip)
            setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
            setCornerRadius(8f)
            setWidth(BalloonSizeSpec.WRAP)
            setMargin(6)
            setBackgroundColorResource(R.color.colorAlwaysLightSurface)
            setBalloonAnimation(BalloonAnimation.ELASTIC)
            build()
        }.apply {
            getContentView().findViewById<AppCompatTextView>(R.id.message).text = if (viewModel.isCurrentUser.value == true) {
                when (citizenship) {
                    UserCitizenship.VISITOR -> context.getString(R.string.idCard_citizenship_action_own_visitor) + context.getString(R.string.idCard_citizenship_action_other_resident)
                    UserCitizenship.RESIDENT -> context.getString(R.string.idCard_citizenship_action_own_resident) + context.getString(R.string.idCard_citizenship_action_other_citizen)
                    UserCitizenship.CITIZEN -> context.getString(R.string.idCard_citizenship_action_own_citizen) + context.getString(R.string.idCard_citizenship_action_other_officer)
                    UserCitizenship.OFFICER -> context.getString(R.string.idCard_citizenship_action_own_officer) + context.getString(R.string.idCard_citizenship_action_other_ambassador)
                    UserCitizenship.AMBASSADOR -> context.getString(R.string.idCard_citizenship_action_own_ambassador) + context.getString(R.string.idCard_citizenship_action_other_ambassador)
                    UserCitizenship.MINISTER -> context.getString(R.string.idCard_citizenship_action_own_minister)
                    UserCitizenship.PRESIDENT -> context.getString(R.string.idCard_citizenship_action_own_president)
                    UserCitizenship.GOLDEN -> context.getString(R.string.idCard_citizenship_action_own_president)
                    else -> ""
                }
            } else when (citizenship) {
                UserCitizenship.VISITOR -> context.getString(
                    R.string.idCard_citizenship_action_other_visitor
                )

                UserCitizenship.RESIDENT -> context.getString(R.string.idCard_citizenship_action_other_resident)
                UserCitizenship.CITIZEN -> context.getString(R.string.idCard_citizenship_action_other_citizen)
                UserCitizenship.OFFICER -> context.getString(R.string.idCard_citizenship_action_other_officer)
                UserCitizenship.AMBASSADOR -> context.getString(R.string.idCard_citizenship_action_other_ambassador)
                UserCitizenship.MINISTER -> context.getString(R.string.idCard_citizenship_action_other_minister)
                UserCitizenship.PRESIDENT -> context.getString(R.string.idCard_citizenship_action_other_president)
                UserCitizenship.GOLDEN -> context.getString(R.string.idCard_citizenship_action_other_president)
            }
        }
    }

    private fun idCardPopUpClick(
        viewModel: PublicUserProfileViewModel,
        context: Context,
        view: View,
        args: PublicUserProfileFragmentArgs,
        onNavigateToSendGift: (() -> Unit)? = null,
        onNavigateToSendFlix: (() -> Unit)? = null,
        onNavigateToRestrict: (() -> Unit)? = null,
        onNavigateToReport: (() -> Unit)? = null,
        onNavigateToBan: (() -> Unit)? = null
    ) {
        val profile = viewModel.profile.value ?: return
        val isMinisterCanBlockVisitor = (viewModel.user.citizenship == UserCitizenship.MINISTER && profile.citizenship == UserCitizenship.VISITOR)
        val notSelf = viewModel.isCurrentUser.value == false
        val canTemporarilyBlockAnyUser = viewModel.user.userEmpowerment?.canTemporarilyBlockAnyUser == true
        val isPresidentUser = viewModel.user.citizenship == UserCitizenship.PRESIDENT || profile.citizenship == UserCitizenship.PRESIDENT

        val popup = PopupMenu(context, view)
        MenuCompat.setGroupDividerEnabled(popup.menu, true)
        popup.menuInflater.inflate(R.menu.menu_idcard, popup.menu)
        // popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.id_card_send_gift).isVisible =
                notSelf /*&& viewModel.user.citizenship != UserCitizenship.VISITOR*/ && viewModel.user.blockedByAdmin == false && profile.blockedByAdmin == false
            findItem(R.id.id_card_send_flax).isVisible =
                notSelf && profile.citizenship != UserCitizenship.VISITOR && viewModel.user.citizenship != UserCitizenship.VISITOR && viewModel.user.blockedByAdmin == false && profile.blockedByAdmin == false
            findItem(R.id.id_card_follow).isVisible = profile.isSuperstar != true && (notSelf)
            findItem(R.id.id_card_follow).setTitle(if (profile.isStar) R.string.user_action_unfollow else R.string.user_action_follow)
            findItem(R.id.id_card_block).setTitle(if (viewModel.chatInfo.value?.chatBlocked == true) R.string.user_action_unblock_from_private_message else R.string.user_action_block_from_private_message)
            findItem(R.id.id_card_block).isVisible = args.context == UserProfileContext.PRIVATE_CHAT && viewModel.chatStatus.value != PublicUserProfileViewModel.PrivateChatStatus.RESTRICTED
            findItem(R.id.id_card_restrict).isVisible = args.context == UserProfileContext.PRIVATE_CHAT && viewModel.chatStatus.value == PublicUserProfileViewModel.PrivateChatStatus.ACTIVE
            findItem(R.id.id_card_manage_notifications).isVisible = args.context == UserProfileContext.STAR_BROADCAST
            findItem(R.id.id_card_send_private_message).isVisible =
                (args.context != UserProfileContext.PRIVATE_CHAT && args.context != UserProfileContext.STAR_BROADCAST || args.context == UserProfileContext.HUDDLE_LIST) && (notSelf)
            findItem(R.id.id_card_remove_broadcast).isVisible = args.context == UserProfileContext.BROADCAST_LIST
            findItem(R.id.id_card_remove_broadcast).setTitle(if (profile.likersPrivacy == false) R.string.user_action_remove_from_broadcast else R.string.user_action_add_to_broadcast)
            findItem(R.id.id_card_ban).isVisible = (notSelf && (canTemporarilyBlockAnyUser || isMinisterCanBlockVisitor)) && !isPresidentUser
            findItem(R.id.id_card_block_from_app).apply {
                //Hiding block from flashat menu item for all users as per requirement
                isVisible = false
                setTitle(if (profile.blockedByLeader == false) R.string.user_action_block_from_app else R.string.user_action_unblock_from_temporarily)
            }
            findItem(R.id.id_card_edit_nick_name).isVisible = notSelf
            findItem(R.id.id_card_report).apply {
                isVisible = notSelf && viewModel.user.canReport(profile)
                setupMenuItemTextColor(color = R.color.colorError, context = context, isTextBold = true)
            }
            findItem(R.id.id_card_ban).apply {
                isVisible = notSelf && viewModel.user.canBan(profile)
                setupMenuItemTextColor(color = R.color.colorError, context = context, isTextBold = true)
            }
        }
        popup.setOnMenuItemClickListener { item ->
            when (item?.itemId) {
                R.id.id_card_send_gift -> onNavigateToSendGift?.let { it() }
                R.id.id_card_send_flax -> onNavigateToSendFlix?.let { it() }
                R.id.id_card_follow -> viewModel.toggleFollow()
                R.id.id_card_block -> if (viewModel.chatInfo.value?.chatBlocked == true) viewModel.unblockChat() else viewModel.blockChat()
                R.id.id_card_restrict -> onNavigateToRestrict?.let { it() }
                R.id.id_card_remove_broadcast -> if (profile.likersPrivacy == true) viewModel.hideLiker(false) else viewModel.hideLiker(true)
                R.id.id_card_send_private_message -> viewModel.navigateToPrivateMessage()
                R.id.id_card_block_from_app -> blockUserTemporarily(viewModel = viewModel, context = context)
                R.id.id_card_edit_nick_name -> handleIDCardNickNameChange(viewModel = viewModel, context = context)
                R.id.id_card_report -> onNavigateToReport?.let { it() }
                R.id.id_card_ban -> onNavigateToBan?.let { it() }
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun handleIDCardNickNameChange(
        viewModel: PublicUserProfileViewModel,
        context: Context,
    ) {
        viewModel.showNickNameEdit()
        MaterialDialog(context).show {
            val view = DataBindingUtil.inflate<LayoutNicknameIdCardEditBinding>(layoutInflater, R.layout.layout_nickname_id_card_edit, null, false)
            view.viewModel = viewModel
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.actionSave.setOnClickListener {
                viewModel.updateUserNickName()
                dismiss()
            }
            view.actionCancel.setOnClickListener {
                viewModel.cancelNickNameEdit()
                dismiss()
            }
        }
    }

    private fun blockUserTemporarily(
        viewModel: PublicUserProfileViewModel,
        context: Context,
    ) {
        if (viewModel.profile.value?.blockedByAdmin != true) {
            if (viewModel.profile.value?.blockedByLeader == false) {
                MaterialAlertDialogBuilder(context, R.style.ThemeOverlay_Flashat_MaterialAlertDialog).setMessage(context.getText(R.string.text_block_temporarily_alert))
                    .setPositiveButton(context.getText(R.string.common_block)) { dialog, _ ->
                        dialog.dismiss()
                        viewModel.blockUserTemporarily()
                    }.setNegativeButton(context.getText(R.string.common_cancel)) { dialog, _ ->
                        dialog.dismiss()
                    }.show()
            } else viewModel.blockUserTemporarily()
        } else Toast.makeText(context, context.getString(R.string.text_block_admin_already_blocked), Toast.LENGTH_SHORT).show()
    }

    fun getRankColor(rank: Int?, context: Context): Int {
        return when {
            rank == 1 -> ContextCompat.getColor(context, R.color.colorBusinessGreen)
            rank in 2..5 -> ContextCompat.getColor(context, R.color.colorPrimary)
            rank in 6..10 -> ContextCompat.getColor(context, R.color.colorSecondaryDark)
            else -> ContextCompat.getColor(context, R.color.colorError)
        }
    }
    fun ItemAffairUserLayoutBinding.bindCitizenshipUserData(stateAffair: UserStateAffair, isLargeScreen: Boolean?) {
        this.stateAffair = stateAffair

        val layoutParams = this.cardLayout.layoutParams
        layoutParams.width = if (isLargeScreen == false)  420 else ViewGroup.LayoutParams.MATCH_PARENT
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        this.cardLayout.layoutParams = layoutParams

        this.cardLayout.radius =25f
        val flagId = CountryListUtil.getCustomCountryMap().getOrElse(stateAffair.countryCode) { 0 }
        this.countryFlag = flagId
        showCrown = false

        when (stateAffair.citizenship) {

            UserCitizenship.MINISTER -> {
                cardLayout.setCardBackgroundColor(ContextCompat.getColor(root.context, R.color.colorPrimary))
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.textColorOnPrimary)
                leftText = stateAffair.dearCount.toString()
                leftDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_dears)
                rightText =this.root.context.getString(R.string.state_affair_gnr_count, stateAffair.gnr?.toInt() ?: 0.0)
                rightDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_lv)
            }

            UserCitizenship.AMBASSADOR-> {
                cardLayout.setCardBackgroundColor(ContextCompat.getColor(root.context, R.color.colorPodiumSpeakerAmbassador))
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.colorPrimaryColorDarkest1)
                leftText = stateAffair.dearCount.toString()
                leftDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_dears)
                rightText = this.root.context.getString(R.string.state_affair_gnr_count, stateAffair.gnr?.toInt() ?: 0.0)
                rightDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_lv)
            }

            UserCitizenship.OFFICER -> {
                cardLayout.setCardBackgroundColor(ContextCompat.getColor(root.context, R.color.colorHuddleOfficerBackground))
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.colorPrimary)
                leftText = stateAffair.dearCount.toString()
                leftDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_dears)
                rightText = this.root.context.getString(R.string.state_affair_gnr_count, stateAffair.gnr?.toInt() ?: 0.0)
                rightDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_lv)
            }

            else -> null
        }

    }

    fun ItemAffairUserLayoutBinding.bindFlashatersData(stateAffair: UserStateAffair, isLargeScreen: Boolean?,listType: StateAffairsTypes) {
        this.stateAffair = stateAffair

        val layoutParams = this.cardLayout.layoutParams
        layoutParams.width = if (isLargeScreen == false) 450 else ViewGroup.LayoutParams.MATCH_PARENT
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        this.cardLayout.layoutParams = layoutParams

        this.cardLayout.radius = 25f
        val flagId = CountryListUtil.getCustomCountryMap().getOrElse(stateAffair.countryCode) { 0 }
        this.countryFlag = flagId

        when (stateAffair.citizenship) {

            UserCitizenship.PRESIDENT ->{
                headerLayout.background = ContextCompat.getDrawable(root.context, R.drawable.bg_president_flix_rate)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.colorPrimaryColorDarkest1)
                rightText = this.root.context.getString(R.string.user_citizenship_president)
            }
            UserCitizenship.GOLDEN->{
                headerLayout.background = ContextCompat.getDrawable(root.context, R.drawable.bg_golden_state_affair)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.white)
                rightText = this.root.context.getString(R.string.user_citizenship_golden)
            }
            UserCitizenship.MINISTER -> {
                headerLayout.background = ContextCompat.getDrawable(root.context, R.color.colorPrimary)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.white)
                rightText = this.root.context.getString(R.string.user_citizenship_minister)
            }
            UserCitizenship.AMBASSADOR -> {
                headerLayout.background = ContextCompat.getDrawable(root.context, R.color.colorPodiumSpeakerAmbassador)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.colorPrimaryColorDarkest1)
                rightText = this.root.context.getString(R.string.user_citizenship_ambassador)
            }
            UserCitizenship.OFFICER -> {
                headerLayout.background = ContextCompat.getDrawable(root.context, R.color.colorHuddleOfficerBackground)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.colorPrimary)
                rightText = this.root.context.getString(R.string.user_citizenship_officer)
            }
            UserCitizenship.CITIZEN->{
                headerLayout.background = ContextCompat.getDrawable(root.context, R.color.colorHuddleTagCitizen)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.colorPrimaryColorDarkest1)
                rightText = this.root.context.getString(R.string.user_citizenship_citizen)
            }
            UserCitizenship.RESIDENT->{
                headerLayout.background = ContextCompat.getDrawable(root.context, R.color.colorHuddleResidentBackground)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.white)
                rightText = this.root.context.getString(R.string.user_citizenship_resident)
            }
            UserCitizenship.VISITOR->{
                headerLayout.background = ContextCompat.getDrawable(root.context, R.color.colorStateAffairVisitor)
                userCitizenshipColor = ContextCompat.getColor(root.context, R.color.textColorAlwaysLightSecondary)
                rightText = this.root.context.getString(R.string.user_citizenship_visitor)
            }
            else -> null
        }

        when (listType) {

            StateAffairsTypes.MOST_GENEROUS_FLASHATERS -> {
                leftText = if(stateAffair.showCrownForGenerosity) null else this.root.context.getString(R.string.state_affair_gnr_count, stateAffair.gnr)
                showCrown = stateAffair.showCrownForGenerosity
                leftDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_lv)
                rightDrawBackground = null
            }
            StateAffairsTypes.MOST_POPULAR_FLASHATERS -> {

                leftText = this.root.context.getString(R.string.state_affair_rank_count, stateAffair.rank.toString())
                itemLeft.textLvl.setTextColor(getRankColor(stateAffair.rank ?: 0, root.context))
                showCrown = false
                leftDrawBackground = null
                rightDrawBackground = null
            }
            StateAffairsTypes.MOST_SKILLFUL_FLASHATERS -> {
                leftText = if(stateAffair.showCrownForSkill) null else  this.root.context.getString(R.string.state_affair_skill_count, stateAffair.skill)
                showCrown = stateAffair.showCrownForSkill
                leftDrawBackground = ContextCompat.getDrawable(root.context, R.drawable.ic_idcard_pl)
                rightDrawBackground = null
            }
            else -> {}
        }

    }



    fun LayoutStateAffairLowGradedUsersBinding.setData(stateAffair: StateAffairsTotalUsers, userCitizenship: UserCitizenship, context: Context){

        when (userCitizenship) {
            UserCitizenship.RESIDENT -> {
                this.layoutData.background = AppCompatResources.getDrawable(context,R.drawable.gradient_state_affair_resident)
                this.textCitizenship.text = context.getString(R.string.state_affairs_Residents)
                this.textCitizenship.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.textTotalUser.text = stateAffair.resident?.total.toString()
                this.textTotalUser.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.totalCurrentMonth.text = stateAffair.resident?.month.toString()
                this.textCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.totalCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.totalCurrentYear.text = stateAffair.resident?.year.toString()
                this.textCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.totalCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.white))

            }
            UserCitizenship.CITIZEN -> {
                this.layoutData.background = AppCompatResources.getDrawable(context,R.drawable.gradient_state_affair_citizen)
                this.textCitizenship.text = context.getString(R.string.state_affairs_citizens)
                this.textCitizenship.setTextColor(ContextCompat.getColor(context,R.color.colorPrimary))
                this.textTotalUser.text = stateAffair.citizen?.total.toString()
                this.textTotalUser.setTextColor(ContextCompat.getColor(context,R.color.colorPrimary))
                this.totalCurrentMonth.text = stateAffair.citizen?.month.toString()
                this.totalCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.totalCurrentYear.text = stateAffair.citizen?.year.toString()
                this.totalCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.textCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.textCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.black))

            }
            UserCitizenship.VISITOR -> {
                this.layoutData.background = AppCompatResources.getDrawable(context,R.drawable.gradient_state_affair_visitor)
                this.textCitizenship.text = context.getString(R.string.state_affairs_visitors)
                this.textCitizenship.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.textTotalUser.text = stateAffair.visitor?.total.toString()
                this.textTotalUser.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.totalCurrentMonth.text = stateAffair.visitor?.month.toString()
                this.totalCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.totalCurrentYear.text = stateAffair.visitor?.year.toString()
                this.totalCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.textCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.textCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.black))

            }
            UserCitizenship.GOLDEN -> {
                this.layoutData.background = AppCompatResources.getDrawable(context,R.drawable.gradient_state_affair_golden)
                this.textCitizenship.text = context.getString(R.string.golden_type)
                this.textCitizenship.setTextColor(ContextCompat.getColor(context,R.color.colorFlashatGolden))
                this.textTotalUser.text = stateAffair.golden?.total.toString()
                this.textTotalUser.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.totalCurrentMonth.text = stateAffair.golden?.month.toString()
                this.totalCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.totalCurrentYear.text = stateAffair.golden?.year.toString()
                this.totalCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.white))
                this.textCurrentYear.setTextColor(ContextCompat.getColor(context,R.color.black))
                this.textCurrentMonth.setTextColor(ContextCompat.getColor(context,R.color.black))

            }
            else -> {}
        }

    }

    private fun getRankColor(rank: Int, context: Context): Int {
        return when {
            rank == 1 -> ContextCompat.getColor(context,R.color.colorBusinessGreen)
            rank in 2..5 -> ContextCompat.getColor(context,R.color.colorPrimary)
            rank in 6..10 -> ContextCompat.getColor(context,R.color.colorSecondary)
            else -> ContextCompat.getColor(context,R.color.colorError)
        }
    }

    @StringRes
    fun HelpSettingsItem.setTitle(): Int {
        return when (this) {
            HelpSettingsItem.GENERAL -> R.string.settings_help_header_general_title
            HelpSettingsItem.CLOUD_HOUSE -> R.string.settings_help_header_cloud_house_title
            HelpSettingsItem.CLOUD_STREET -> R.string.settings_help_header_cloud_street_title
            HelpSettingsItem.CLOUD_OFFICE -> R.string.settings_help_header_cloud_office_title
            HelpSettingsItem.CLOUD_AUTHORITIES -> R.string.settings_help_header_cloud_authorities_title
            HelpSettingsItem.DELETE_ACCOUNT -> R.string.settings_title_delete_account
        }
    }
    fun HelpSettingsItem.setSubTitle():Int{
        return when (this) {
            HelpSettingsItem.GENERAL -> R.string.settings_help_header_general_sub_title
            HelpSettingsItem.CLOUD_HOUSE -> R.string.settings_help_header_cloud_house_sub_title
            HelpSettingsItem.CLOUD_STREET -> R.string.settings_help_header_cloud_street_sub_title
            HelpSettingsItem.CLOUD_OFFICE -> R.string.settings_help_header_cloud_office_sub_title
            HelpSettingsItem.CLOUD_AUTHORITIES -> R.string.settings_help_header_cloud_authorities_sub_title
            HelpSettingsItem.DELETE_ACCOUNT -> R.string.settings_sub_title_delete_account
        }
    }

    fun DocumentType.setText():Int {
        return when (this) {
            DocumentType.TERMS_OF_USE -> R.string.settings_title_terms_of_use
            DocumentType.USERNAME_GUIDE_LINES -> R.string.settings_title_username_guidelines
            DocumentType.COMMUNITY_GUIDE_LINES -> R.string.settings_title_community_guidelines
            DocumentType.PRIVACY_POLICY -> R.string.settings_title_privacy_policy
            DocumentType.COOKIES_POLICY -> R.string.settings_title_cookies_policy
            DocumentType.HUDDLE_POLICY -> R.string.settings_title_huddle_policy
            DocumentType.GROUP_POLICY -> R.string.settings_title_groups_policy
            DocumentType.PP_RULES_REGULATIONS -> R.string.settings_title_pp_policy
            DocumentType.GIFT_POLICY -> R.string.settings_help_flahat_gifts
            DocumentType.PODIUM_POLICY -> R.string.settings_title_podium_policy
            DocumentType.FLAX_POLICY -> R.string.settings_title_pp_policy
            DocumentType.LEGAL_AFFAIRS_ABOUT -> R.string.settings_help_about_legal_affairs
            DocumentType.STATE_AFFAIRS_ABOUT -> R.string.settings_help_about_state_affairs

            DocumentType.ABOUT_FLASHAT -> R.string.settings_title_about_flashat
            DocumentType.FLIX_AND_COINS -> R.string.settings_title_flix_and_coins
            DocumentType.USERS_LEVELS -> R.string.settings_title_users_level
            DocumentType.USERS_STRENGTH -> R.string.settings_title_users_strength
            DocumentType.PERSONAL_DATA -> R.string.settings_title_personal_data
            DocumentType.ID_CARD -> R.string.settings_title_id_card

            DocumentType.ABOUT_BUDDIES -> R.string.settings_title_about_buddies
            DocumentType.ABOUT_INTRUDERS -> R.string.settings_title_about_intruders

            DocumentType.ABOUT_FLASH ->R.string.settings_title_about_flash
            DocumentType.ABOUT_POSTAT -> R.string.settings_title_about_postat

            DocumentType.ABOUT_TASKS->R.string.settings_title_about_tasks
            DocumentType.ABOUT_DEALS -> R.string.settings_title_about_deals
            DocumentType.ABOUT_STATEMENTS -> R.string.settings_title_about_statements

            DocumentType.ABOUT_PRESIDENTIAL_AFFAIRS ->R.string.settings_title_about_presidential_affairs
            DocumentType.ABOUT_SOCIAL_AFFAIRS -> R.string.settings_title_about_social_affairs
            else -> R.string.common_upgrade
        }
    }



}