package com.app.messej.ui.utils

import android.app.Activity
import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.util.Size
import android.view.ActionMode
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import android.widget.PopupWindow
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.text.BidiFormatter
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.ChatTextColor.Companion.orDefault
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.LayoutChatColorPickerDialogBinding
import com.app.messej.databinding.LayoutChatColorPickerDialogItemBinding
import com.app.messej.ui.utils.markdown.BoldEditHandler
import com.app.messej.ui.utils.markdown.ItalicEditHandler
import com.app.messej.ui.utils.markdown.UnderlineEditHandler
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.ScaleInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.material.textfield.TextInputLayout
import com.google.android.material.textfield.TextInputLayout.END_ICON_CUSTOM
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import io.noties.markwon.AbstractMarkwonPlugin
import io.noties.markwon.Markwon
import io.noties.markwon.MarkwonConfiguration
import io.noties.markwon.MarkwonSpansFactory
import io.noties.markwon.MarkwonVisitor
import io.noties.markwon.RenderProps
import io.noties.markwon.SoftBreakAddsNewLinePlugin
import io.noties.markwon.editor.MarkwonEditor
import io.noties.markwon.editor.MarkwonEditorTextWatcher
import io.noties.markwon.html.HtmlPlugin
import io.noties.markwon.html.tag.UnderlineHandler
import org.commonmark.node.Code
import org.commonmark.node.Emphasis
import org.commonmark.node.Image
import org.commonmark.node.Link
import org.commonmark.node.LinkReferenceDefinition
import org.commonmark.parser.Parser
import java.text.Bidi
import java.util.concurrent.Executors


object TextFormatUtils {

    fun String.highlightOccurrences(highlight: String, style:()-> Any) = SpannableString(this).highlightOccurrences(highlight, style)

    fun SpannableString.highlightOccurrences(highlight: String, style: () -> Any): SpannableString {
        if (highlight.isBlank()) return this
        val baseText = this.toString()
        return try {
            val indexes = mutableListOf<Int>()
            var index = baseText.indexOf(highlight, ignoreCase = true)
            while (index >= 0) {
                indexes.add(index)
                index = baseText.indexOf(highlight, index + highlight.length, ignoreCase = true)
            }
            indexes.forEach { start ->
                setSpan(style.invoke(), start, start + highlight.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            this
        } catch (e: Exception) {
            Firebase.crashlytics.log("Was trying to style '${highlight}' in '${baseText}' with ${style.invoke().javaClass}")
            Firebase.crashlytics.recordException(e)
            this
        }
    }

    fun SpannableString.highlightMentions(c: Context?, users: List<MentionedUser>, currentUser: Int, onClick: ((MentionedUser) -> Unit)? = null): SpannableString {
        val span = SpannableStringBuilder(this)
        var matches: List<MatchResult>
        while (true) {
            matches = UserInfoUtil.mentionEncodedRegex.findAll(span).filter { matchResult ->
                val userId = matchResult.groupValues[1].toIntOrNull()
                users.find { it.id == userId }!=null
            }.toList()
            if (matches.isEmpty()) break
            val matchResult = matches[0]
            Log.w("HUDMEN", "applying mention highlight to |${matchResult.value}|")
            val userId = matchResult.groupValues[1].toIntOrNull()
            val user = users.find { it.id == userId }?: continue
            val start = matchResult.range.first
            val end = matchResult.range.last+1
            val bidiFormatter = BidiFormatter.getInstance()
            val token = if (user.id==currentUser) {
                SpannableString(bidiFormatter.unicodeWrap(" ${MentionTokenizer.TOKEN_START_CHAR}${user.userNickNameOrName} ")).apply {
                    c?:return@apply
                    setSpan(RoundedBackgroundSpan(c, R.color.colorPrimary, R.color.textColorOnPrimary, 3), 0, length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            } else {
                SpannableString(bidiFormatter.unicodeWrap("${MentionTokenizer.TOKEN_START_CHAR}${user.userNickNameOrName}")).apply {
                    c?:return@apply
                    setSpan(ForegroundColorSpan(ContextCompat.getColor(c, R.color.colorPrimaryDark)), 0, length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    onClick?.let { clickLis ->
                        setSpan(object : ClickableSpan() {
                            override fun updateDrawState(ds: TextPaint) {
                                ds.isUnderlineText = false
                            }

                            override fun onClick(widget: View) {
                                Log.w("HUDMEN", "clicked on ${user.username}")
                                clickLis.invoke(user)
                            }
                        }, 0,length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                }
            }
            span.replace(start,end,token)
        }
        return SpannableString(span)
    }

    fun String.bidiFormatMentions(users: List<AbstractUser>): String {
        var newText = this
        val bidiFormatter = BidiFormatter.getInstance()
        users.forEach { user ->
            newText = newText.replace(
                "${MentionTokenizer.TOKEN_START_CHAR}${user.username}",
                bidiFormatter.unicodeWrap("${MentionTokenizer.TOKEN_START_CHAR}${user.username}")
            )
        }
        return newText
    }

    fun highlightMentionsForEdit(c: Context, text: String, users: List<AbstractUser>): SpannableString {
        val converted = UserInfoUtil.decodeMentions(text, users)
        val span = SpannableString(converted)
        Log.w("HUDMEN", "decoded mentions $text")
        UserInfoUtil.mentionDecodedRegex.findAll(converted).forEach { matchResult ->
            Log.w("HUDMEN", "applying mention highlight to ${matchResult.value}")
            users.find { it.username == matchResult.groupValues[1] }?: return@forEach
            val start = matchResult.range.first
            val end = matchResult.range.last+1
            span.setSpan(ForegroundColorSpan(ContextCompat.getColor(c, R.color.colorPrimaryDark)), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return span
    }

    fun String.applyMarkdownFormatting(c: Context): SpannableString {
        val markwon = getMarkDownParser(c)
        val node = markwon.parse(this)
        val _markdown = markwon.render(node)
        return SpannableString(_markdown)
    }

    fun TextView.setFormattedText(text: String, color: ChatTextColor?, forceLight: Boolean = false) {
        this.text = text.applyMarkdownFormatting(context)
        Log.w("TFU", "setFormattedText: color is $color for $text")
        color.orDefault().let {
            val textColor = ContextCompat.getColor(context, if (forceLight) it.alwaysLightColorRes else it.colorRes)
            setTextColor(textColor)
        }
    }

    private val wordRegex = Regex("[\\w\\d]+")

    private fun getMarkDownParser(c: Context): Markwon {
        return Markwon.builder(c)
            .usePlugin(HtmlPlugin.create { plugin ->
                plugin.excludeDefaults(true)
                plugin.addHandler(UnderlineHandler())
            })
            .usePlugin(SoftBreakAddsNewLinePlugin.create())
            .usePlugin(object: AbstractMarkwonPlugin() {
                override fun configureParser(builder: Parser.Builder) {
                    builder.enabledBlockTypes(emptySet())
                }

                override fun configureSpansFactory(builder: MarkwonSpansFactory.Builder) {
                    builder.setFactory(Emphasis::class.java) { configuration: MarkwonConfiguration?, props: RenderProps? ->
                        ItalicEditHandler.Italic()
                    }
                }

                override fun configureVisitor(builder: MarkwonVisitor.Builder) {
                    builder
//                        .on(Block::class.java,null)
                        .on(Code::class.java,null)
//                        .on(HardLineBreak::class.java,null)
//                        .on(HtmlInline::class.java,null)
                        .on(Image::class.java,null)
                        .on(Link::class.java,null)
                        .on(LinkReferenceDefinition::class.java,null)
//                        .on(SoftLineBreak::class.java,null)
                }
            })
            .build()
    }

    interface TextFormattingListener {

        fun canInteract(): Boolean
        fun getSelectedColor() : ChatTextColor
        fun onSelectColor(color: ChatTextColor)
    }

    fun TextInputLayout.enableTextFormatting(activity: Activity, listener: TextFormattingListener, dropDownColorPicker: Boolean = false) {
        val editor: MarkwonEditor = MarkwonEditor.builder(getMarkDownParser(activity))
            .useEditHandler(BoldEditHandler)
            .useEditHandler(ItalicEditHandler)
            .useEditHandler(UnderlineEditHandler)
            .build()
        this.editText?.apply {
            addTextChangedListener(
                MarkwonEditorTextWatcher.withPreRender(editor, Executors.newCachedThreadPool(), this
                ))
            customSelectionActionModeCallback = object : ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    // Checking Mention Feature is there
                    val hasWords = wordRegex.containsMatchIn(text.substring(selectionStart, selectionEnd))
                    if (hasWords) {
                        mode?.menuInflater?.inflate(R.menu.menu_text_format, menu)
                    }
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return true
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    val start = selectionStart
                    val end = selectionEnd
                    when (item?.itemId) {
                        R.id.action_bold -> {
                            text.insert(end, "**")
                            text.insert(start, "**")
                        }
                        R.id.action_italics -> {
                            text.insert(end, "_")
                            text.insert(start, "_")
                        }
                        R.id.action_under_line -> {
                            text.insert(end, "</u>");
                            text.insert(start, "<u>");
                        }
                        else -> {
                            return false
                        }
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {}
            }
        }

        // Enable color picker

        endIconMode = END_ICON_CUSTOM
        setEndIconDrawable(R.drawable.ic_text_format_color)
        isEndIconCheckable = true
        setEndIconOnClickListener { view ->
            if (!listener.canInteract()) return@setEndIconOnClickListener
            val inflater = LayoutInflater.from(view.context)
            val binding = DataBindingUtil.inflate<LayoutChatColorPickerDialogBinding>(inflater, R.layout.layout_chat_color_picker_dialog, null, false)

            val popup = PopupWindow().apply {
                contentView = binding.root
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                isFocusable = true
            }

            val currentColor = listener.getSelectedColor()

            val mAdapter = object : BaseQuickAdapter<ChatTextColor, BaseDataBindingHolder<LayoutChatColorPickerDialogItemBinding>>(R.layout.layout_chat_color_picker_dialog_item, ChatTextColor.entries.toMutableList()) {
                override fun convert(holder: BaseDataBindingHolder<LayoutChatColorPickerDialogItemBinding>, item: ChatTextColor) {
                    holder.dataBinding?.apply {
                        colorDot.setImageResource(item.colorRes)
                        selected = currentColor == item
                        ContextCompat.getColor(context,item.colorRes)
                    }
                }
            }.apply {
                animationEnable = true
                adapterAnimation = ScaleInAnimation()
                isAnimationFirstOnly = true

                setOnItemClickListener{ adapter, view, position ->
                    val color = data[position]
                    setEndIconTintList(ViewUtils.getColorStateList(activity,color.colorRes))
                    popup.dismiss()
                    listener.onSelectColor(color)
                }
            }

            binding.colorList.apply {
                layoutManager = GridLayoutManager(context,5)
                setHasFixedSize(true)
                adapter = mAdapter
            }
            popup.apply {
                // Absolute location of the anchor view
                contentView.apply {
                    measure(
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                    )
                }
                val location = IntArray(2).apply {
                    view.getLocationOnScreen(this)
                }
                val popupSize = Size(
                    contentView.measuredWidth,
                    contentView.measuredHeight
                )

                showAtLocation(
                    view,
                    Gravity.TOP or Gravity.START,
                    (location[0] - (popupSize.width - view.width)).coerceAtLeast(0),
                    if(dropDownColorPicker) location[1]+view.height else location[1]-popupSize.height
                )
            }
//            popup.showAsDropDown(view,0,(-0.2 * view.height).roundToInt(), Gravity.BOTTOM)
        }
    }

    fun CharSequence.isLeftToRight(): Boolean {
        val bidi = Bidi(this.toString(), Bidi.DIRECTION_DEFAULT_LEFT_TO_RIGHT)
        return bidi.baseIsLeftToRight()
    }

    fun AppCompatTextView.setAsMandatory(isSpaceBetweenText: Boolean = true) {
        val updatedText = "${this.text}" + if (isSpaceBetweenText) " *" else "*"
        this.text = updatedText.highlightOccurrences(
            highlight = "*",
            style = { ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorError)) }
        )
    }
}