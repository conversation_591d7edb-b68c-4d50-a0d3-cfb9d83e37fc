package com.app.messej.ui.home.privatetab.messages

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.databinding.FragmentPrivateMessagesPendingBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView


class PrivateMessagePendingListFragment : Fragment() {

    private lateinit var binding: FragmentPrivateMessagesPendingBinding

    private var mAdapter: PrivateMessagesAdapter? = null

    private val viewModel: PrivateMessagePendingListViewModel by viewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_private_messages_pending, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.let {
            LayoutListStateEmptyBinding.bind(it).prepare(
                image = R.drawable.im_eds_chats,
                message = R.string.private_messages_pending_eds,
            )
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.private_messages_action_restricted)
    }

    private fun observe() {
        viewModel.chatList.observe(viewLifecycleOwner) {
            it?: return@observe
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PrivateMessagesAdapter(layoutInflater, viewModel.user.id, object: PrivateMessagesAdapter.ItemListener {
            override fun onItemClick(item: PrivateChat, position: Int) {
                findNavController().navigateSafe(PrivateMessagePendingListFragmentDirections.actionGlobalNavigationChatPrivate(item.id,item.receiver))
            }
        })

        val layoutMan = LinearLayoutManager(context)

        binding.huddleList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            currentUserId = viewModel.user.id

            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)

                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }
}