package com.app.messej.ui.home.settings.about

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.HelpSettingsItem
import com.app.messej.ui.utils.BindingExtensions.setSubTitle
import com.app.messej.ui.utils.BindingExtensions.setText
import com.app.messej.ui.utils.BindingExtensions.setTitle

interface OnItemSelected{
    fun onItemSelected(item: DocumentType)
    fun onDeletedClicked()
}

@Composable
fun SettingsHelpComposeScreen(listener: OnItemSelected) {
    val generalSubList =
        listOf(DocumentType.ABOUT_FLASHAT,DocumentType.PRIVACY_POLICY, DocumentType.TERMS_OF_USE, DocumentType.USERNAME_GUIDE_LINES, DocumentType.COMMUNITY_GUIDE_LINES, DocumentType.COOKIES_POLICY,DocumentType.FLIX_AND_COINS,DocumentType.GIFT_POLICY,DocumentType.USERS_LEVELS,DocumentType.USERS_STRENGTH,DocumentType.PERSONAL_DATA,DocumentType.ID_CARD)
    val cloudHouseSubList = listOf(DocumentType.ABOUT_BUDDIES,DocumentType.GROUP_POLICY,DocumentType.ABOUT_INTRUDERS)
    val cloudStreetSubList = listOf(DocumentType.HUDDLE_POLICY,DocumentType.ABOUT_FLASH,DocumentType.ABOUT_POSTAT,DocumentType.PODIUM_POLICY)
    val cloudOfficeSubList = listOf(DocumentType.ABOUT_TASKS, DocumentType.ABOUT_DEALS, DocumentType.ABOUT_STATEMENTS)
    val cloudAuthoritiesSubList = listOf(DocumentType.LEGAL_AFFAIRS_ABOUT,DocumentType.ABOUT_PRESIDENTIAL_AFFAIRS,DocumentType.STATE_AFFAIRS_ABOUT,DocumentType.ABOUT_SOCIAL_AFFAIRS)
    val title = listOf(
        Pair(HelpSettingsItem.GENERAL, generalSubList),
        Pair(HelpSettingsItem.CLOUD_HOUSE, cloudHouseSubList),
        Pair(HelpSettingsItem.CLOUD_STREET, cloudStreetSubList),
        Pair(HelpSettingsItem.CLOUD_OFFICE, cloudOfficeSubList),
        Pair(HelpSettingsItem.CLOUD_AUTHORITIES, cloudAuthoritiesSubList),
        Pair(HelpSettingsItem.DELETE_ACCOUNT, null)

    )
    var expandedType by remember { mutableStateOf<HelpSettingsItem?>(null) }
    LazyColumn(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
        contentPadding = PaddingValues(vertical = dimensionResource(id = R.dimen.activity_margin))
    ) {
        items(title.size) { position ->

            val item = title[position]
            Column(modifier = Modifier.fillMaxWidth()) {
                SettingsHelpSingleItem(
                    title = stringResource(item.first.setTitle()), subTitle = stringResource(item.first.setSubTitle()), requiredSubTitle = true, onSelectedItemClick = {
                        if (item.first == HelpSettingsItem.DELETE_ACCOUNT) {
                            listener.onDeletedClicked()
                        } else {
                            expandedType = if (expandedType == item.first) {
                                null
                            } else {
                                item.first
                            }
                        }
                    }, isExpanded = expandedType == item.first
                )
                AnimatedVisibility(visible = expandedType == item.first) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = dimensionResource(id = R.dimen.element_spacing))
                    ) {
                        repeat(item.second?.size ?: 0) {
                            val documentType = item.second?.get(it)
                            SettingsHelpSingleItem(
                                title = documentType?.setText()?.let { name -> stringResource(name) }, requiredSubTitle = false, onSelectedItemClick = { documentType?.let { item -> listener.onItemSelected(item) } })
                        }
                    }
                }

            }

        }
    }
}

@Preview(showBackground = true, uiMode = 33)
@Composable
fun SettingsHelpItemPreview() {
    SettingsHelpComposeScreen(object : OnItemSelected{
        override fun onItemSelected(item: DocumentType) {
        }

        override fun onDeletedClicked() {

        }
    })
}