package com.app.messej.ui.home.publictab.huddles.chat

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuCompat
import androidx.core.view.MenuProvider
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserRole
import com.app.messej.databinding.LayoutImageViewerHeaderBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportContentAllowed
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.app.messej.ui.legal.report.ReportUtils.canReportAndHide
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.stfalcon.imageviewer.StfalconImageViewer
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


abstract class BaseHuddleChatFragment : BaseChatDisplayFragment(), HuddleChatAdapter.HuddleChatClickListener {

    abstract override val viewModel: HuddleChatViewModel
    private var apiLoader : MaterialDialog? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override val reversedLayout: Boolean
        get() = false

    private fun setup() {

    }

    private fun showAPILoader() {
        apiLoader = showLoader()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {

        viewModel.onUserBlocked.observe(viewLifecycleOwner) {
            Toast.makeText(context,it, Toast.LENGTH_SHORT).show()
        }

        viewModel.onUserFollow.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }

        viewModel.onBlockedUser.observe(viewLifecycleOwner) {blockedUser ->
            Toast.makeText(context, getString(R.string.huddle_blocked_user_toast_message, blockedUser), Toast.LENGTH_SHORT).show()
        }

        viewModel.onSharePostLinkGenerated.observe(viewLifecycleOwner){
           shareHuddlePost(it)
        }

        viewModel.onUnblockedUser.observe(viewLifecycleOwner) {unblockedUser ->
            Toast.makeText(context, getString(R.string.huddle_unblocked_user_toast_message, unblockedUser), Toast.LENGTH_SHORT).show()
        }

        viewModel.onReportMessage.observe(viewLifecycleOwner) {
            val msg= it as HuddleChatMessage
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportToManagerFragment(msg.huddleIdInt, msg.messageId, null, ReportToManagerType.MESSAGE))
        }

        viewModel.onFailedToSendAdminInvite.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(activity, resources.getString(R.string.huddle_admin_request_limit_exceed_toast_sender), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            val action = NavGraphHomeDirections.actionGlobalNavigationChatPrivate(it.first, it.second)
            findNavController().navigateSafe(action)
        }

        viewModel.enableChatInteraction.observe(viewLifecycleOwner) {
            it.let { 1 }
            // Do not remove this as its required for livedata to trigger
        }

        viewModel.huddle.observe(viewLifecycleOwner) {
            it.let { 1 }
            // Do not remove this as its required for livedata to trigger
        }

        viewModel.onPostPinned.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(),if (it) R.string.chat_huddle_message_action_pin_success else R.string.chat_huddle_message_action_unpin_success, Toast.LENGTH_SHORT).show()
        }

        viewModel.onPostEditReady.observe(viewLifecycleOwner) {
            navigateToPostSheet()
        }

        viewModel.messageSending.observe(viewLifecycleOwner) {
            hideKeyboard()
        }

        viewModel.showLoadingDialog.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }
    }

    override fun customizeImageOverlay(viewer: StfalconImageViewer<OfflineMedia>, headerBinding: LayoutImageViewerHeaderBinding, msg: AbstractChatMessageWithMedia) {
        headerBinding.toolbar.addMenuProvider(object: MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {

                menuInflater.inflate(R.menu.menu_chat_image_fullscreen_with_actions,menu)
                menu.findItem(R.id.action_delete).isVisible = false
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                when (menuItem.itemId) {
                    R.id.action_save_to_gallery -> viewModel.saveToGallery(msg.offlineMedia)
                    R.id.action_delete -> confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message,false,
                                                        viewModel.canDeleteMessageForEveryOne(msg)) {
                        viewModel.deleteMessage(msg.message, it)
                        viewer.close()
                    }
                    else -> return false
                }
                return true
            }

        },viewLifecycleOwner)
    }

    override fun onUpgradeClick() {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
    }

    override fun onItemLike(item: AbstractChatMessage, position: Int) {
        if (viewModel.enableChatInteraction.value!=true) return
        viewModel.likeItem(item)
    }

    open fun customizeMessageOptions(popup: PopupMenu) {

    }

    override fun onItemLongClick(item: AbstractChatMessage, position: Int) {
        if(viewModel.userEmpowerment.canDeleteAnyHuddlePost && viewModel.huddle.value?.empoweredUserBlocked == false) { //Empowered user can select and delete post (f he is not blocked by another empowered user)
            super.onItemLongClick(item, position)
            return
        }
        //To block actions on case : admin blocked
        if (viewModel.entryBlocked() || viewModel.huddleStatus.value != GroupChatStatus.ACTIVE) return
        super.onItemLongClick(item, position)
    }

    override fun onMessageOptionsClick(chat: HuddleChatMessageWithMedia, position: Int, view: View) {
        val empoweredUserActionCheck = !viewModel.userEmpowerment.canBlockAnyUserInHuddle
                || viewModel.huddle.value?.empoweredUserBlocked == true //check block permission is there and current empowered user(me) is not blocked by an empowered user
        //To block actions on case : admin blocked || Disable pop menu when "chatSelectionMode" is ON || empowered user conditions
        if ((viewModel.entryBlocked() && empoweredUserActionCheck) || viewModel.chatSelectionMode.value == true) return

        val msg = chat.message

        Log.d("RPF", "chat: $msg")

        if (viewModel.entryBlocked() || viewModel.huddleStatus.value != GroupChatStatus.ACTIVE) return

        val popup = PopupMenu(requireContext(),view)
        MenuCompat.setGroupDividerEnabled(popup.menu, true)
        popup.menuInflater.inflate(R.menu.menu_chat_huddle_message_actions, popup.menu)
        popup.setForceShowIcon(true)
        val intervention = viewModel.getInterventionForUser(msg.sender)

        val iAmElevated = viewModel.huddle.value?.role?.isElevated==true
        val iamManager = viewModel.huddle.value?.role==UserRole.MANAGER
        val iAmAdmin =  viewModel.huddle.value?.role==UserRole.ADMIN
        val iamPremium = viewModel.user.premium
        val userIsDeleted = msg.senderDetails?.deletedAccount==true
        val userIsElevated = msg.senderDetails?.role?.isElevated==true
        val userIsManager = msg.senderDetails?.role==UserRole.MANAGER
        val userIsPremium = msg.senderDetails?.premium==true
        val isPresidentChat = msg.senderDetails?.citizenship == UserCitizenship.PRESIDENT
        val userHasHuddleBlockEmpowerment = viewModel.userEmpowerment.canBlockAnyUserInHuddle

        val managerIsPremium = viewModel.huddle.value?.managerPremium == true
        val isOwnPost = viewModel.user.id == msg.sender

        val iAmPremiumAdmin = iAmAdmin && iamPremium

        val isCommentBanned = intervention?.commentBanned == true
        val isPostBanned = intervention?.postBanned == true
        val isReplyBanned = intervention?.replayBanned == true

        val isTribe=viewModel.huddle.value?.isTribe==true
        val isReported = msg.reported

        val isSticker=msg.hasSticker
        val isGolden = msg.senderDetails?.citizenship?.isGolden?:false

        popup.menu.apply {

            findItem(R.id.action_view_info).isEnabled = !userIsDeleted
            findItem(R.id.action_view_info).isVisible = !isOwnPost

            findItem(R.id.action_edit_post).isVisible = userIsPremium && isOwnPost && msg.mediaType!=MediaType.AUDIO && msg.messageType!=AbstractChatMessage.MessageType.STICKER

            findItem(R.id.action_private_message).isVisible = !isOwnPost && iamPremium
            findItem(R.id.action_private_message).isEnabled = !userIsDeleted

            findItem(R.id.action_block_from_huddle).isVisible = msg.senderDetails?.role != null && (iAmElevated ||  userHasHuddleBlockEmpowerment)&& !isPresidentChat && !userIsManager && !isOwnPost && !isReported && !isGolden
            findItem(R.id.action_block_from_huddle).isEnabled = !userIsDeleted
            intervention?.let { int ->
                findItem(R.id.action_block_from_huddle).setTitle(if (int.blockedFromHuddle) R.string.chat_huddle_message_action_block_from_huddle_cancel else R.string.chat_huddle_message_action_block_from_huddle)
            }
//            findItem(R.id.action_block_user).isEnabled = !userDeleted

            findItem(R.id.action_reply_to_post).isVisible = !isReported

            findItem(R.id.action_pin_message).isVisible = iamManager && !isReported
            findItem(R.id.action_pin_message).setTitle(if (msg.pinned == true) R.string.chat_huddle_message_action_unpin_message else R.string.chat_huddle_message_action_pin_message)

            findItem(R.id.action_admin).isVisible = !isOwnPost && !userIsManager && iAmElevated && userIsPremium && !isTribe && msg.senderDetails?.role != null && !isReported
            findItem(R.id.action_admin).setTitle(
                if (userIsElevated) R.string.chat_huddle_message_action_remove_admin
                else if (intervention?.invitedToBeAdmin == true) R.string.chat_huddle_message_action_admin_invite_cancel
                else R.string.chat_huddle_message_action_make_admin
            )

            findItem(R.id.action_report).isVisible = !isOwnPost && viewModel.enableChatInteraction.value == true && !isGolden
            findItem(R.id.action_report).setTitle(if (msg.reported) R.string.chat_huddle_message_action_report_cancel else R.string.user_action_report_to_manager)

            findItem(R.id.action_delete_post).isVisible = false

            // Hide for now but enable this option later
            //findItem(R.id.action_delete_post).isVisible = iAmElevated
            Log.d("BanSTATUS", "Ban status: $isPostBanned $isCommentBanned $isReplyBanned")

            findItem(R.id.ban_posting).let {
                it.isVisible = (managerIsPremium || iAmPremiumAdmin) && !isOwnPost && iAmElevated && (msg.senderDetails?.role != null) && !userIsManager && !isReported && !isGolden
                it.setTitle( if (!isPostBanned) { R.string.huddle_ban_from_posting } else { R.string.huddle_unban_from_posting } )
            }

            findItem(R.id.ban_replying).let {
                it.isVisible = (managerIsPremium || iAmPremiumAdmin) && !isOwnPost && iAmElevated && !userIsManager && (msg.senderDetails?.role != null) && !isReported && !isGolden
                it.setTitle( if (!isReplyBanned) {  R.string.huddle_ban_from_replying } else { R.string.huddle_unban_from_replying } )
            }
            findItem(R.id.ban_commenting).let {
                it.isVisible = (managerIsPremium || iAmPremiumAdmin) && !isOwnPost && iAmElevated && !userIsManager && (msg.senderDetails?.role != null) && !isReported && !isGolden
                it.setTitle( if (!isCommentBanned) { R.string.huddle_ban_from_commenting } else { R.string.huddle_unban_from_commenting } )
            }

            findItem(R.id.remove_participant).isVisible = ((managerIsPremium || iAmPremiumAdmin) && !isOwnPost) && iAmElevated && (msg.senderDetails?.role != null) && !userIsManager && !isReported

            findItem(R.id.share_posting).isVisible= userIsPremium && !isReported && !isTribe && intervention?.userBlocked!=true && intervention?.blockedFromHuddle!=true

            findItem(R.id.action_forward).isVisible= !isSticker &&  intervention?.blockedFromHuddle==false && intervention.userBlocked==false && !isReported

            findItem(R.id.action_report_legal).apply {
                isVisible = !isOwnPost && viewModel.user.canReport(msg.senderDetails?.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
            findItem(R.id.action_report_and_hide).apply {
                isVisible = !isOwnPost && viewModel.user.canReportAndHide(msg.senderDetails?.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
            findItem(R.id.action_ban).apply {
                isVisible = !isOwnPost && viewModel.user.canBan(msg.senderDetails?.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
        }
        popup.setOnMenuItemClickListener { menuItem ->

            when(menuItem.itemId) {
                R.id.action_view_info -> {
//
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(msg.sender))
                }
                R.id.action_forward->findNavController().navigateSafe(HuddleChatFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.HUDDLES, messageId = msg.messageId))
                R.id.action_edit_post -> {
                    viewModel.checkPermissionToUpdateHuddle(chat)
//                    viewModel.editPost(chat)
                }
                R.id.action_private_message -> viewModel.navigateToPrivateMessage(msg.sender)
                R.id.action_report -> viewModel.reportMessage(msg)
                R.id.action_block_from_huddle -> {
                    if (intervention?.blockedFromHuddle==true) viewModel.blockUserFromHuddle(msg.sender, Participant.ParticipantsActionTypes.UNBLOCK_HUDDLE_PARTICIPANT)
                    else confirmAction(
                        title = R.string.chat_huddle_message_action_block_from_huddle,
                        message = resources.getString(R.string.other_user_huddle_block_confirm, msg.senderDetails?.name?:""),
                        positiveTitle = R.string.common_yes,
                        negativeTitle = R.string.common_no
                    ) {
                        viewModel.blockUserFromHuddle(msg.sender, Participant.ParticipantsActionTypes.BLOCK_HUDDLE_PARTICIPANT)
                    }
                }
                R.id.action_admin -> {
                    if (msg.senderDetails?.role?.isElevated==true) viewModel.dismissAdmin(msg.sender)
                    else if(intervention?.invitedToBeAdmin==true) viewModel.cancelAdminInvite(msg.sender)
                    else viewModel.appointAsAdmin(msg.sender)
                }

                R.id.action_delete_post -> {
                    confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message,false,
                                  viewModel.canDeleteSelectionForEveryone.value == true) {
                        viewModel.deleteMessage(msg, it)
                    }
                }
                R.id.action_reply_to_post -> onItemReply(msg, position)
                R.id.action_pin_message -> viewModel.pinMessage(msg,msg.pinned==false)
                R.id.ban_posting-> viewModel.banUser(Participant.ParticipantStatus.POST_BAN, isPostBanned, msg.sender)
                R.id.ban_replying-> viewModel.banUser(Participant.ParticipantStatus.REPLY_BAN, isReplyBanned, msg.sender)
                R.id.ban_commenting-> viewModel.banUser(Participant.ParticipantStatus.COMMENT_BAN, isCommentBanned, msg.sender)
                R.id.remove_participant-> viewModel.removeUser(msg.sender)
                R.id.share_posting->{
                    viewModel.getSharePostLink(msg.messageId)
                }
                R.id.action_report_legal-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.HuddlePost(msg, reportType = ReportType.REPORT).serialize()))
                    }
                }
                R.id.action_report_and_hide-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.HuddlePost(msg, reportType = ReportType.REPORT_AND_HIDE).serialize()))
                    }
                }
                R.id.action_ban-> {
                    ensureReportBanAllowed {
                        msg.senderDetails?.asBasicUser()?.let {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(it, reportType = ReportType.BAN).serialize()))
                        }
                    }
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        customizeMessageOptions(popup)
        popup.show()
    }

    private var streamPrepareJob: Job? = null

    override fun onViewHolderCleanup(messageId: String) {
        super.onViewHolderCleanup(messageId)
        streamPrepareJob?.cancel()
        streamPrepareJob = null
    }

    override fun onStreamMedia(view: PlayerView, msg: AbstractChatMessageWithMedia, position: Int): Boolean {
        streamPrepareJob?.let {
            it.cancel()
            streamPrepareJob = null
        }
        streamPrepareJob = viewLifecycleOwner.lifecycleScope.launch {
            try {
                val url = viewModel.getVideoStreamingURL(msg, position) ?: return@launch
                if (!isActive) return@launch
                val media = MediaItem.fromUri(url)
                viewModel.onStreamingVideo(msg, url, position)
                playVideo(view, media)
            } finally {
                streamPrepareJob = null
            }
        }
        return true
    }

    override fun onFollowClick(msg: HuddleChatMessage) {
        if (viewModel.entryBlocked() || viewModel.huddleStatus.value != GroupChatStatus.ACTIVE) return
        viewModel.followUser(msg.sender)
    }

    private fun shareHuddlePost(link: String) {
        if (link.isNotEmpty()) {
            val text = resources.getString(R.string.public_share_huddle_link_text,link)
            val sendIntent: Intent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_TEXT, text)
                type = "text/plain"
            }
            val shareIntent = Intent.createChooser(sendIntent, getString(R.string.common_share))
            startActivity(shareIntent)
        }
    }

    protected open fun navigateToPostSheet() {

    }


}